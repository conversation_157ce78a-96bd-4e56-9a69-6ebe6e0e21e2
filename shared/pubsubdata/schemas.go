package pubsubdata

import (
	"time"
)

// All of the subscription topic pairs for Pubsub
var PubsubSubscriptions = map[string]string{
	"etl-processing-dlq-messages":              "dlq-messages",
	"etl-processing-dlq-bqbatch":               "dlq-bqbatch",
	"etl-processing-gateway-rmsData":           "broker-gateway-rmsData",
	"etl-raw-gateway-rmsData":                  "broker-gateway-rmsData",
	"etl-processing-gateway-rmsEngine":         "broker-gateway-rmsEngine",
	"etl-raw-gateway-rmsEngine":                "broker-gateway-rmsEngine",
	"etl-processing-gateway-monitorName":       "broker-gateway-monitorName",
	"etl-raw-gateway-monitorName":              "broker-gateway-monitorName",
	"etl-processing-gateway-macAddress":        "broker-gateway-macAddress",
	"etl-raw-gateway-macAddress":               "broker-gateway-macAddress",
	"etl-processing-gateway-faultLogs":         "broker-gateway-faultLogs",
	"etl-raw-gateway-faultLogs":                "broker-gateway-faultLogs",
	"etl-processing-gateway-perfStats":         "broker-gateway-perfStats",
	"etl-raw-gateway-perfStats":                "broker-gateway-perfStats",
	"etl-processing-gateway-gatewayLog":        "broker-gateway-gatewayLog",
	"etl-raw-gateway-gatewayLog":               "broker-gateway-gatewayLog",
	"etl-processing-gateway-faultNotification": "broker-gateway-faultNotification",
	"etl-raw-gateway-faultNotification":        "broker-gateway-faultNotification",
	"notification-alerts-sub":                  "notification-alerts-topic",
	"etl-purge-expired-data":                   "broker-purge-expired-data",
}

// All of the topic names for Pubsub
var Topics = []string{
	"dlq-messages",
	"dlq-bqbatch", // This is a DLQ for the DLQ incase BigQuery beings to fail.
	"broker-gateway-rmsData",
	"broker-gateway-rmsEngine",
	"broker-gateway-monitorName",
	"broker-gateway-macAddress",
	"broker-gateway-faultLogs",
	"broker-gateway-perfStats",
	"broker-gateway-gatewayLog",
	"broker-gateway-faultNotification",
	"notification-alerts-topic",
	"broker-purge-expired-data",
}

type CommonAttributes struct {
	Topic                  string
	OrganizationIdentifier string
	DeviceType             string
	DLQReason              string
	ReceiveTimestamp       time.Time
}

// HeaderDetails is a custom type that list all of the headers we will send to pubsub
type HeaderDetails struct {
	Host            string `json:"Host"`
	UserAgent       string `json:"User-Agent"`
	ContentLength   string `json:"Content-Length"`
	ContentType     string `json:"Content-Type"`
	GatewayDeviceID string `json:"gateway-device-id"`
	MessageVersion  string `json:"message-version"`
	MessageType     string `json:"message-type"`
	APIKey          string `json:"x-api-key,omitempty"` // Gets removed before sending
	GatewayTimezone string `json:"tz"`
}

// PubsubMessageWrapper is a helper struct that contains the exported fields from pubsub.Message.
type PubsubMessageWrapper struct {
	ID              string            `json:"id"`
	Data            []byte            `json:"data"`
	Attributes      map[string]string `json:"attributes,omitempty"`
	OrderingKey     string            `json:"ordering_key,omitempty"`
	PublishTime     time.Time         `json:"publish_time,omitempty"`
	DeliveryAttempt int64             `json:"delivery_attempt,omitempty"`
}
