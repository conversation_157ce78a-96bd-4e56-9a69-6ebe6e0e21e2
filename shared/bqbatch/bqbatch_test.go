package bqbatch

import (
	"context"
	"reflect"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/mocks/bqexecutor"
)

// Validate that the default table/struct pairs are valid.
func TestDefaultTableStructPairs(t *testing.T) {
	t.<PERSON>()
	assert := assert.New(t)

	// Check that the default table/struct pairs are valid.
	for _, q := range queues {
		assert.NotEmpty(q.table, "Table name should not be empty")
		_, _, err := getValidType(q.rowExample)
		assert.NoError(err, "Default table/struct pairs should be valid")
	}
}

// Verify that the creation of the default batcher succeeds.
func TestDefaultBatcher(t *testing.T) {
	t.Parallel()
	_, err := newDefault(&bqexecutor.FakeBigQueryExecutor{
		Ctx: context.Background(),
	}, nil)
	assert.NoError(t, err, "newDefault should not return an error")
}

func TestNewCreatesExpectedQueues(t *testing.T) {
	t.<PERSON>()
	exec := &bqexecutor.FakeBigQueryExecutor{Ctx: context.Background()}

	b := New(exec, nil)
	// Ensure returned type is our concrete *batcher
	bb, ok := b.(*batcher)
	if !ok {
		t.Fatalf("New() returned %T, want *batcher", b)
	}

	// Verify the number of queues matches the configs
	if got, want := len(bb.queues), len(bb.cfgs); got != want {
		t.Errorf("len(queues) = %d; want %d", got, want)
	}

	// Check each queue's table name and config
	for name, cfg := range bb.cfgs {
		q, exists := bb.queues[name]
		if !exists {
			t.Errorf("queue for %q missing", name)
			continue
		}
		if q.table != name {
			t.Errorf("q.table = %q; want %q", q.table, name)
		}
		if !reflect.DeepEqual(q.cfg, cfg) {
			t.Errorf("q.cfg = %+v; want %+v", q.cfg, cfg)
		}
	}
}

func TestExtraShutdown(t *testing.T) {
	t.Parallel()
	exec := &bqexecutor.FakeBigQueryExecutor{Ctx: context.Background()}

	b := New(exec, nil)
	// Ensure returned type is our concrete *batcher
	bb, ok := b.(*batcher)
	if !ok {
		t.Fatalf("New() returned %T, want *batcher", b)
	}

	assert.NoError(t, bb.Shutdown(), "Shutdown should not return an error on first call")
	assert.ErrorIs(t, ErrBatcherIsShutDown, bb.Shutdown(), "Shutdown should return an error on second call")
}

// fakeBatcher just implements the interface; methods never actually run.
type fakeBatcher struct{}

// LoadBatch implements Batcher.
func (f *fakeBatcher) LoadBatch(batch FailedBatch) error {
	panic("unimplemented")
}

func (f *fakeBatcher) Register(_ any, _ string, _ QueueConfig) error {
	return nil
}
func (f *fakeBatcher) Add(_ any) error           { return nil }
func (f *fakeBatcher) Shutdown() error           { return nil }
func (f *fakeBatcher) AddTest(_ BatchTest) error { return nil }

func TestWithBatch_GetBatch_Success(t *testing.T) {
	t.Parallel()
	assert := assert.New(t)

	// No batch was ever stored --> ErrBatchContext
	_, err := GetBatch(context.Background())
	assert.ErrorIs(err, ErrBatchContext)

	// Create a fake Batch and stick it in a context
	fb := &fakeBatcher{}
	ctx := context.Background()
	ctx = WithBatch(ctx, fb)

	// Retrieving it should succeed and give the same pointer back
	got, err := GetBatch(ctx)
	assert.NoError(err)
	assert.Same(fb, got)
}

// getValidType tests

// Example struct that should produce a valid schema.
type ValidStruct struct {
	A int
	B string
}

// Pointer to a struct with an unsupported field type.
type BadStruct struct {
	F func()
}

func TestGetValidType_TableDriven(t *testing.T) {
	tests := []struct {
		name         string
		input        any
		expectType   reflect.Type
		expectSchema bool  // whether a non-nil schema is expected
		expectErr    error // either bqbatch.ErrNotStruct or non-nil from InferSchema
	}{
		{
			name:       "nil input",
			input:      nil,
			expectType: nil,
			expectErr:  ErrNotStruct,
		},
		{
			name:       "non-struct input (int)",
			input:      42,
			expectType: nil,
			expectErr:  ErrNotStruct,
		},
		{
			name:         "valid struct value",
			input:        ValidStruct{A: 1, B: "x"},
			expectType:   reflect.TypeOf(ValidStruct{}),
			expectSchema: true,
			expectErr:    nil,
		},
		{
			name:         "pointer to valid struct",
			input:        &ValidStruct{A: 2, B: "y"},
			expectType:   reflect.TypeOf(ValidStruct{}),
			expectSchema: true,
			expectErr:    nil,
		},
		{
			name:       "struct with unsupported field",
			input:      BadStruct{F: func() {}},
			expectType: nil,
			// The error will come from bigquery.InferSchema, not ErrNotStruct
			expectErr: func() error {
				_, err := bigquery.InferSchema(BadStruct{})
				return err
			}(),
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			gotType, gotSchema, gotErr := getValidType(tc.input)

			// Check error expectation
			if tc.expectErr != nil {
				assert.Error(t, gotErr)
				// For ErrNotStruct cases, exactly match
				if tc.expectErr == ErrNotStruct {
					assert.Equal(t, ErrNotStruct, gotErr)
				} else {
					// Otherwise just ensure the error strings match
					assert.EqualError(t, gotErr, tc.expectErr.Error())
				}
				assert.Nil(t, gotType)
				assert.Nil(t, gotSchema)
				return
			}
			assert.NoError(t, gotErr)

			// Type should match expected reflect.Type
			assert.Equal(t, tc.expectType, gotType)

			// Schema should be non-nil if expected
			if tc.expectSchema {
				assert.NotNil(t, gotSchema)
				// Verify schema has fields "A" and "B"
				fieldNames := []string{}
				for _, f := range gotSchema {
					fieldNames = append(fieldNames, f.Name)
				}
				assert.Contains(t, fieldNames, "A")
				assert.Contains(t, fieldNames, "B")
			} else {
				assert.Nil(t, gotSchema)
			}
		})
	}
}

func TestSplitBatch(t *testing.T) {
	tests := []struct {
		name     string
		rows     []PreSerializedRow
		maxSize  int
		expected [][]PreSerializedRow
	}{
		{
			name:     "empty rows",
			rows:     []PreSerializedRow{},
			maxSize:  100,
			expected: [][]PreSerializedRow{},
		},
		{
			name: "single row under limit",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 20},
			},
			maxSize: 100,
			expected: [][]PreSerializedRow{
				{{JSONBytes: []byte(`{"key":"value1"}`), Size: 20}},
			},
		},
		{
			name: "single row at limit",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 100},
			},
			maxSize: 100,
			expected: [][]PreSerializedRow{
				{{JSONBytes: []byte(`{"key":"value1"}`), Size: 100}},
			},
		},
		{
			name: "single row over limit",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 150},
			},
			maxSize: 100,
			expected: [][]PreSerializedRow{
				{{JSONBytes: []byte(`{"key":"value1"}`), Size: 150}},
			},
		},
		{
			name: "multiple rows fit in one batch",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 20},
				{JSONBytes: []byte(`{"key":"value2"}`), Size: 25},
				{JSONBytes: []byte(`{"key":"value3"}`), Size: 30},
			},
			maxSize: 100,
			expected: [][]PreSerializedRow{
				{
					{JSONBytes: []byte(`{"key":"value1"}`), Size: 20},
					{JSONBytes: []byte(`{"key":"value2"}`), Size: 25},
					{JSONBytes: []byte(`{"key":"value3"}`), Size: 30},
				},
			},
		},
		{
			name: "rows split into multiple batches",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 40},
				{JSONBytes: []byte(`{"key":"value2"}`), Size: 50},
				{JSONBytes: []byte(`{"key":"value3"}`), Size: 30},
				{JSONBytes: []byte(`{"key":"value4"}`), Size: 35},
			},
			maxSize: 100,
			expected: [][]PreSerializedRow{
				{
					{JSONBytes: []byte(`{"key":"value1"}`), Size: 40},
					{JSONBytes: []byte(`{"key":"value2"}`), Size: 50},
				},
				{
					{JSONBytes: []byte(`{"key":"value3"}`), Size: 30},
					{JSONBytes: []byte(`{"key":"value4"}`), Size: 35},
				},
			},
		},
		{
			name: "exact size boundary",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 50},
				{JSONBytes: []byte(`{"key":"value2"}`), Size: 50},
				{JSONBytes: []byte(`{"key":"value3"}`), Size: 50},
			},
			maxSize: 100,
			expected: [][]PreSerializedRow{
				{
					{JSONBytes: []byte(`{"key":"value1"}`), Size: 50},
					{JSONBytes: []byte(`{"key":"value2"}`), Size: 50},
				},
				{
					{JSONBytes: []byte(`{"key":"value3"}`), Size: 50},
				},
			},
		},
		{
			name: "large row forces split",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 30},
				{JSONBytes: []byte(`{"key":"value2"}`), Size: 120}, // Over limit
				{JSONBytes: []byte(`{"key":"value3"}`), Size: 25},
			},
			maxSize: 100,
			expected: [][]PreSerializedRow{
				{
					{JSONBytes: []byte(`{"key":"value1"}`), Size: 30},
				},
				{
					{JSONBytes: []byte(`{"key":"value2"}`), Size: 120},
				},
				{
					{JSONBytes: []byte(`{"key":"value3"}`), Size: 25},
				},
			},
		},
		{
			name: "zero max size",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 20},
				{JSONBytes: []byte(`{"key":"value2"}`), Size: 25},
			},
			maxSize: 0,
			expected: [][]PreSerializedRow{
				{
					{JSONBytes: []byte(`{"key":"value1"}`), Size: 20},
				},
				{
					{JSONBytes: []byte(`{"key":"value2"}`), Size: 25},
				},
			},
		},
		{
			name: "negative max size",
			rows: []PreSerializedRow{
				{JSONBytes: []byte(`{"key":"value1"}`), Size: 20},
				{JSONBytes: []byte(`{"key":"value2"}`), Size: 25},
			},
			maxSize: -10,
			expected: [][]PreSerializedRow{
				{
					{JSONBytes: []byte(`{"key":"value1"}`), Size: 20},
				},
				{
					{JSONBytes: []byte(`{"key":"value2"}`), Size: 25},
				},
			},
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			result := splitBatch(tc.rows, tc.maxSize)

			// Check number of batches
			assert.Equal(t, len(tc.expected), len(result), "Number of batches should match")

			// Check each batch
			for i, expectedBatch := range tc.expected {
				assert.Equal(t, len(expectedBatch), len(result[i]), "Batch %d size should match", i)

				// Check each row in the batch
				for j, expectedRow := range expectedBatch {
					assert.Equal(t, expectedRow.Size, result[i][j].Size, "Row %d in batch %d size should match", j, i)
					assert.Equal(t, expectedRow.JSONBytes, result[i][j].JSONBytes, "Row %d in batch %d JSON should match", j, i)
				}
			}
		})
	}
}

func TestSplitDLQBatch(t *testing.T) {
	tests := []struct {
		name     string
		batch    FailedBatch
		maxSize  int
		expected []FailedBatch
	}{
		{
			name: "empty batch",
			batch: FailedBatch{
				Table:      "test_table",
				Timestamp:  time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Error:      "test error",
				Rows:       []map[string]any{},
				RetryCount: 1,
			},
			maxSize: 100,
			expected: []FailedBatch{
				{
					Table:      "test_table",
					Timestamp:  time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:      "test error",
					Rows:       []map[string]any{},
					RetryCount: 1,
				},
			},
		},
		{
			name: "single row under limit",
			batch: FailedBatch{
				Table:     "test_table",
				Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Error:     "test error",
				Rows: []map[string]any{
					{"key": "value1"},
				},
				RetryCount: 1,
			},
			maxSize: 1000, // Large enough to fit
			expected: []FailedBatch{
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value1"},
					},
					RetryCount: 1,
				},
			},
		},
		{
			name: "multiple rows fit in one batch",
			batch: FailedBatch{
				Table:     "test_table",
				Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Error:     "test error",
				Rows: []map[string]any{
					{"key": "value1"},
					{"key": "value2"},
					{"key": "value3"},
				},
				RetryCount: 1,
			},
			maxSize: 1000, // Large enough to fit
			expected: []FailedBatch{
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value1"},
						{"key": "value2"},
						{"key": "value3"},
					},
					RetryCount: 1,
				},
			},
		},
		{
			name: "rows split into multiple batches",
			batch: FailedBatch{
				Table:     "test_table",
				Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Error:     "test error",
				Rows: []map[string]any{
					{"key": "value1", "data": "large_data_1"},
					{"key": "value2", "data": "large_data_2"},
					{"key": "value3", "data": "large_data_3"},
					{"key": "value4", "data": "large_data_4"},
				},
				RetryCount: 1,
			},
			maxSize: 200, // Small enough to force split
			expected: []FailedBatch{
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value1", "data": "large_data_1"},
						{"key": "value2", "data": "large_data_2"},
					},
					RetryCount: 1,
				},
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value3", "data": "large_data_3"},
						{"key": "value4", "data": "large_data_4"},
					},
					RetryCount: 1,
				},
			},
		},
		{
			name: "zero max size",
			batch: FailedBatch{
				Table:     "test_table",
				Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Error:     "test error",
				Rows: []map[string]any{
					{"key": "value1"},
					{"key": "value2"},
				},
				RetryCount: 1,
			},
			maxSize: 0,
			expected: []FailedBatch{
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value1"},
					},
					RetryCount: 1,
				},
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value2"},
					},
					RetryCount: 1,
				},
			},
		},
		{
			name: "negative max size",
			batch: FailedBatch{
				Table:     "test_table",
				Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Error:     "test error",
				Rows: []map[string]any{
					{"key": "value1"},
					{"key": "value2"},
				},
				RetryCount: 1,
			},
			maxSize: -10,
			expected: []FailedBatch{
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value1"},
					},
					RetryCount: 1,
				},
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{"key": "value2"},
					},
					RetryCount: 1,
				},
			},
		},
		{
			name: "complex nested data",
			batch: FailedBatch{
				Table:     "test_table",
				Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Error:     "test error",
				Rows: []map[string]any{
					{
						"id":   1,
						"data": map[string]any{"nested": "value1"},
						"tags": []string{"tag1", "tag2"},
					},
					{
						"id":   2,
						"data": map[string]any{"nested": "value2"},
						"tags": []string{"tag3", "tag4"},
					},
				},
				RetryCount: 1,
			},
			maxSize: 1000, // Large enough to fit
			expected: []FailedBatch{
				{
					Table:     "test_table",
					Timestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
					Error:     "test error",
					Rows: []map[string]any{
						{
							"id":   1,
							"data": map[string]any{"nested": "value1"},
							"tags": []string{"tag1", "tag2"},
						},
						{
							"id":   2,
							"data": map[string]any{"nested": "value2"},
							"tags": []string{"tag3", "tag4"},
						},
					},
					RetryCount: 1,
				},
			},
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			result, err := splitDLQBatch(tc.batch, tc.maxSize)

			// Should not return error
			assert.NoError(t, err)

			// Check number of batches
			assert.Equal(t, len(tc.expected), len(result), "Number of batches should match")

			// Check each batch
			for i, expectedBatch := range tc.expected {
				assert.Equal(t, expectedBatch.Table, result[i].Table, "Batch %d table should match", i)
				assert.Equal(t, expectedBatch.Timestamp, result[i].Timestamp, "Batch %d timestamp should match", i)
				assert.Equal(t, expectedBatch.Error, result[i].Error, "Batch %d error should match", i)
				assert.Equal(t, expectedBatch.RetryCount, result[i].RetryCount, "Batch %d retry count should match", i)
				assert.Equal(t, len(expectedBatch.Rows), len(result[i].Rows), "Batch %d row count should match", i)

				// Check each row in the batch
				for j, expectedRow := range expectedBatch.Rows {
					assert.Equal(t, expectedRow, result[i].Rows[j], "Row %d in batch %d should match", j, i)
				}
			}
		})
	}
}

func TestSplitDLQBatchErrorHandling(t *testing.T) {
	// Test with invalid JSON that would cause marshaling to fail
	// This is a bit tricky to test since the function doesn't actually return errors
	// but we can test edge cases

	t.Run("nil batch", func(t *testing.T) {
		// This would cause a panic in the current implementation
		// We should handle this gracefully
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("splitDLQBatch panicked with nil batch: %v", r)
			}
		}()

		// Create a batch with nil rows
		batch := FailedBatch{
			Table:      "test_table",
			Timestamp:  time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			Error:      "test error",
			Rows:       nil,
			RetryCount: 1,
		}

		result, err := splitDLQBatch(batch, 100)
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, batch.Table, result[0].Table)
		assert.Equal(t, batch.Error, result[0].Error)
		assert.Equal(t, batch.RetryCount, result[0].RetryCount)
		assert.Nil(t, result[0].Rows)
	})
}
