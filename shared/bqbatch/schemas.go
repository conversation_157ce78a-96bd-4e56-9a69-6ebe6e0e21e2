package bqbatch

import (
	"time"

	"cloud.google.com/go/bigquery"
)

// The table used for the integration test.
var T_BQBATCH = "test_bqbatch"

type BatchTest struct {
	Key   string        `bigquery:"key"`
	Value string        `bigquery:"value"`
	Obj   *BatchTestObj `bigquery:"obj"`
	Arr   []bool        `bigquery:"arr"`
}

type BatchTestObj struct {
	Flag1 bool `bigquery:"flag1"`
	Flag2 bool `bigquery:"flag2"`
}

// Pubsub wrapper fallback incase batch load fails
type FailedBatch struct {
	Table      string           `json:"table"`
	Timestamp  time.Time        `json:"timestamp"`
	Error      string           `json:"error"`
	Rows       []map[string]any `json:"rows"`
	RetryCount int              `json:"retry_count"`
}

// QueueConfig holds per-queue flush criteria.
type QueueConfig struct {
	MaxSize              int           // number of rows to trigger flush
	FlushInterval        time.Duration // time to trigger flush
	MetricsFlushInterval time.Duration // time to trigger metrics flush
	MaxConcurrency       int           // maximum concurrent BigQuery jobs (default: 3)
}

// PreSerializedRow holds the JSON bytes and size for a row
// Used for efficient batch splitting
type PreSerializedRow struct {
	ValueSaver bigquery.ValueSaver
	JSONBytes  []byte
	Size       int
	RowMap     map[string]bigquery.Value
}

// PreSerializedDLQRow holds the JSON bytes and size for a DLQ row
// Used for efficient DLQ batch splitting
type PreSerializedDLQRow struct {
	RowMap    map[string]any
	JSONBytes []byte
	Size      int
}
