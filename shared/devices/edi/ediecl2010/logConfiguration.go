package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	CF2018LogLength                          = 85
	ConfigurationChangeLogRecordsCountLength = 1
	ConfigurationChangeLogRecordsCountOffset = HeaderLength
	ConfigurationChangeLogRecordsStartOffset = ConfigurationChangeLogRecordsCountOffset + 1
)

/*
Byte Message Format
===================

+-------------------------------+
|Header (7 bytes)               |
+-------------------------------+
|Number of records (1 byte)     |
+-------------------------------+
|Record #1 (85 bytes)           |
+-------------------------------+
|Record #2 (85 bytes)           |
+-------------------------------+
|              ...              |
+-------------------------------+
|Record #N (85 bytes)           |
+-------------------------------+
|Checksum (1 byte)              |
+-------------------------------+

Header and number of records (8 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte
[7] Number of records (0-255)

Record Structure (85 bytes per record)
-----------------------------------
[8-61] 	Conflict map
[59-61] 	Red Fail Enable
[62-64] 	Green Yellow Dual Enable
[65-67] 	Yellow Red Dual Enable
[68-70] 	Green Red Dual Enable
[71-73] 	Minimum Yellow Clearance Enable
[74-76] 	Reserved bytes
[77-79] 	Yellow Disable
[80-84] 	Reserved bytes
[85] Second in BCD format (00-59)
[86] Minute in BCD format (00-59)
[87] Hour in BCD format (00-23)
[88] Day in BCD format (01-31)
[89] Month in BCD format (01-12)
[90] Year in BCD format (00-99)
[91-92] Reserved bytes

Checksum (1 byte)
---------------
[last byte] XOR of all previous bytes
*/

// LogConfiguration parses the configuration change log records from the byte message
func (device EDIECL2010) LogConfiguration(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.ConfigurationChangeLogRecords, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	if err = validateDeviceInfo(header); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrUnsupportedDevice, err)
	}

	numRecords := int(byteMsg[ConfigurationChangeLogRecordsCountOffset])
	byteMessageLength := HeaderLength + ConfigurationChangeLogRecordsCountLength + numRecords*CF2018LogLength + ChecksumLength
	allRecords = &helper.ConfigurationChangeLogRecords{
		DeviceModel: header.Model.String(),
		RawMessage:  byteMsg,
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	for idx := range numRecords {
		offset := ConfigurationChangeLogRecordsStartOffset + idx*CF2018LogLength
		configurationChangeLogBytes := byteMsg[offset : offset+CF2018LogLength]
		record, err := parseConfigurationChangeLogRecord(configurationChangeLogBytes, httpHeader, header)
		if err != nil {
			return nil, fmt.Errorf("failed to parse configuration change log record %d: %w", idx, err)
		}
		allRecords.Record = append(allRecords.Record, *record)
	}

	return allRecords, nil
}

func parseConfigurationChangeLogRecord(configLogBytes []byte, httpHeader *pubsubdata.HeaderDetails, header *helper.HeaderRecord) (record *helper.ConfigurationChangeLogRecord, err error) {
	record = &helper.ConfigurationChangeLogRecord{}

	// Parse conflict map
	conflictMap := parseConflictMap(configLogBytes[:54])

	// Parse enable flags
	record.RedFailEnable = parseEnableFlags(getIntFromLhMsHs(configLogBytes[51], configLogBytes[52], configLogBytes[53]), 16)
	record.GreenYellowDualEnable = parseEnableFlags(getIntFromLhMsHs(configLogBytes[54], configLogBytes[55], configLogBytes[56]), 16)
	record.YellowRedDualEnable = parseEnableFlags(getIntFromLhMsHs(configLogBytes[57], configLogBytes[58], configLogBytes[59]), 16)
	record.GreenRedDualEnable = parseEnableFlags(getIntFromLhMsHs(configLogBytes[60], configLogBytes[61], configLogBytes[62]), 16)
	record.MinimumYellowClearanceEnable = parseEnableFlags(getIntFromLhMsHs(configLogBytes[63], configLogBytes[64], configLogBytes[65]), 16)
	record.YellowEnable = parseEnableFlags(getIntFromLhMsHs(^configLogBytes[69], ^configLogBytes[70], ^configLogBytes[71]), 16)

	// Parse channel permissives
	parseChannelPermissives(conflictMap, int(header.MaxChannels), record)

	// Parse options and select bytes
	parseOptions1(configLogBytes[73], configLogBytes[72], record)
	parseFlashingYellowArrows(configLogBytes[73], configLogBytes[74], record)
	parseSelect1(configLogBytes[75], record)
	parseSelect2(configLogBytes[76], record)

	// Parse date-time
	record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
		configLogBytes[81], configLogBytes[80], configLogBytes[82],
		configLogBytes[79], configLogBytes[78], configLogBytes[77],
		httpHeader.GatewayTimezone,
	)
	if err != nil {
		return nil, err
	}

	// Parse CRC
	crc := (int(configLogBytes[83]) << 8) | int(configLogBytes[84])
	record.CheckValue = fmt.Sprintf("%d (0x%x)", crc, crc)

	return record, nil
}

// parseConflictMap extracts conflict map data from configLogBytes.
func parseConflictMap(configLogBytes []byte) [18]int {
	var conflictMap [18]int
	for i := 0; i < 18; i++ {
		start := i * 3
		conflictMap[i] = getIntFromLhMsHs(configLogBytes[start], configLogBytes[start+1], configLogBytes[start+2])
	}
	return conflictMap
}

// parseEnableFlags extracts enable/disable flags for a given field from a value.
func parseEnableFlags(value int, count int) []bool {
	result := make([]bool, count)
	mask := 1
	for i := 0; i < count; i++ {
		if value&mask > 0 {
			result[i] = true
		} else {
			result[i] = false
		}
		mask <<= 1
	}
	return result
}

// parseChannelPermissives populates permissive settings for each channel.
func parseChannelPermissives(conflictMap [18]int, maxChannels int, record *helper.ConfigurationChangeLogRecord) {
	for primCh := 1; primCh <= maxChannels-1; primCh++ {
		primaryMask := 1 << (primCh - 1)
		mask := primaryMask
		for secCh := primCh + 1; secCh <= maxChannels; secCh++ {
			if conflictMap[primCh-1]&mask == 0 {
				permStr := fmt.Sprintf("%d", secCh)
				switch primCh {
				case 1:
					record.Ch01Permissives = append(record.Ch01Permissives, permStr)
				case 2:
					record.Ch02Permissives = append(record.Ch02Permissives, permStr)
				case 3:
					record.Ch03Permissives = append(record.Ch03Permissives, permStr)
				case 4:
					record.Ch04Permissives = append(record.Ch04Permissives, permStr)
				case 5:
					record.Ch05Permissives = append(record.Ch05Permissives, permStr)
				case 6:
					record.Ch06Permissives = append(record.Ch06Permissives, permStr)
				case 7:
					record.Ch07Permissives = append(record.Ch07Permissives, permStr)
				case 8:
					record.Ch08Permissives = append(record.Ch08Permissives, permStr)
				case 9:
					record.Ch09Permissives = append(record.Ch09Permissives, permStr)
				case 10:
					record.Ch10Permissives = append(record.Ch10Permissives, permStr)
				case 11:
					record.Ch11Permissives = append(record.Ch11Permissives, permStr)
				case 12:
					record.Ch12Permissives = append(record.Ch12Permissives, permStr)
				case 13:
					record.Ch13Permissives = append(record.Ch13Permissives, permStr)
				case 14:
					record.Ch14Permissives = append(record.Ch14Permissives, permStr)
				case 15:
					record.Ch15Permissives = append(record.Ch15Permissives, permStr)
				}
			}
			mask <<= 1
		}
	}
}

// parseOptions1 processes options1 and param bytes for various settings.
func parseOptions1(options1, param byte, record *helper.ConfigurationChangeLogRecord) {
	if options1&0x01 > 0 {
		record.RedFaultTiming = "1200-1500 ms"
	} else {
		record.RedFaultTiming = "700-1000 ms"
	}
	if options1&0x02 > 0 {
		record.RecurrentPulse = false
	} else {
		record.RecurrentPulse = true
	}
	if options1&0x04 > 0 {
		record.WatchdogTiming = "1 second"
	} else {
		record.WatchdogTiming = "1.5 seconds"
	}
	if param&0x1 > 0 {
		record.WatchdogEnableSwitch = true
	} else {
		record.WatchdogEnableSwitch = false
	}
	if options1&0x08 > 0 {
		record.GYEnable = true
	} else {
		record.GYEnable = false
	}
	if options1&0x20 > 0 {
		record.LEDguardThresholds = true
	} else {
		record.LEDguardThresholds = false
	}
	if options1&0x40 > 0 {
		record.RedFailEnabledbySSM = true
	} else {
		record.RedFailEnabledbySSM = false
	}
}

// parseFlashingYellowArrows processes options1 and options2 for flashing yellow arrows.
func parseFlashingYellowArrows(options1, options2 byte, record *helper.ConfigurationChangeLogRecord) {
	if options2&0xf > 0 {
		if options2&0x1 > 0 {
			record.FlashingYellowArrows = append(record.FlashingYellowArrows, "1-9")
		}
		if options2&0x2 > 0 {
			record.FlashingYellowArrows = append(record.FlashingYellowArrows, "3-10")
		}
		if options2&0x4 > 0 {
			record.FlashingYellowArrows = append(record.FlashingYellowArrows, "5-11")
		}
		if options2&0x8 > 0 {
			record.FlashingYellowArrows = append(record.FlashingYellowArrows, "7-12")
		}
		if options1&0x80 > 0 {
			record.FlashingYellowArrows = append(record.FlashingYellowArrows, "(Mode=FYAC)")
		} else {
			record.FlashingYellowArrows = append(record.FlashingYellowArrows, "(Mode=FYA)")
		}
	} else {
		record.FlashingYellowArrows = append(record.FlashingYellowArrows, "<none>")
	}
}

// parseSelect1 processes select1 byte for various settings.
func parseSelect1(select1 byte, record *helper.ConfigurationChangeLogRecord) {
	if select1&0x1 > 0 {
		record.WDTErrorClearonPU = false
	} else {
		record.WDTErrorClearonPU = true
	}
	if select1&0x2 > 0 {
		record.MinimumFlash = true
	} else {
		record.MinimumFlash = false
	}
	if select1&0x4 > 0 {
		record.ConfigChangeFault = true
	} else {
		record.ConfigChangeFault = false
	}
	if select1&0x8 > 0 {
		record.RedCableFault = true
	} else {
		record.RedCableFault = false
	}
	if select1&0x10 > 0 {
		record.AcLineBrownout = "98 +/- 2Vrms"
	} else {
		record.AcLineBrownout = "92 +/- 2 Vrms"
	}
}

// parseSelect2 processes select2 byte for various settings.
func parseSelect2(select2 byte, record *helper.ConfigurationChangeLogRecord) {
	if select2&0x1 > 0 {
		record.PinEEPolarity = "INVERT"
	} else {
		record.PinEEPolarity = "NORMAL"
	}
	if select2&0x2 > 0 {
		record.DualIndicationFaultTiming = "700 - 1000 ms"
	} else {
		record.DualIndicationFaultTiming = "350 - 500 ms"
	}
	if select2&0x40 > 0 {
		record.FYAFlashRateFault = false
	} else {
		record.FYAFlashRateFault = true
	}
}

func getIntFromLhMsHs(lh, ms, hs byte) int {
	return int(lh)<<16 | int(ms)<<8 | int(hs)
}
