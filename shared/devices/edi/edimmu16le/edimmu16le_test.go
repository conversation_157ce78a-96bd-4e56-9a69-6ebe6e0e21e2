package edimmu16le

import (
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestLogMonitorReset(t *testing.T) {
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	hdr := &helper.HeaderRecord{}

	// buildMsgMonitorReset builds the full payload including count+checksum
	// (same as your production helper)
	buildMsgMonitorReset := func(nRecs int, recs [][]byte) []byte {
		const HeaderLength = 7
		const RecordSize = 7
		total := HeaderLength + 1 + nRecs*RecordSize + 1
		msg := make([]byte, total)
		msg[7] = byte(nRecs)
		pos := HeaderLength + 1
		for _, r := range recs {
			copy(msg[pos:], r)
			pos += RecordSize
		}
		// one's‐complement checksum over all but last byte
		var sum byte
		for _, b := range msg[:len(msg)-1] {
			sum += b
		}
		msg[len(msg)-1] = ^sum
		return msg
	}

	// makeRec(year,month,day,minute,hour,second,type)
	makeRec := func(year, month, day, hour, minute, second, typ byte) []byte {
		return []byte{second, minute, hour, day, month, year, typ}
	}

	// reference time = 2014-02-28 05:16:45 UTC
	wantTime := time.Date(2014, 2, 28, 5, 16, 45, 0, time.UTC)

	tests := []struct {
		name      string
		recs      [][]byte
		wantErr   error
		wantTypes []string
	}{
		{
			name:    "TooShort",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:    "BadLength",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:    "BadChecksum",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrMsgByteChecksum,
		},
		// invalid BCD time bytes
		{
			name:    "InvalidMonthBCD",
			recs:    [][]byte{makeRec(0x14, 0x1A, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name:    "InvalidDayBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x32, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name:    "InvalidHourBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x25, 0x16, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name:    "InvalidMinBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x61, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name:    "InvalidSecBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x61, 1)},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
		{
			name:      "Type1",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantTypes: []string{"MONITOR MANUAL RESET EVENT #"},
		},
		{
			name:      "Type2",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 2)},
			wantTypes: []string{"MONITOR NON-LATCHED FAULT RESET EVENT #"},
		},
		{
			name:      "Type4",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 4)},
			wantTypes: []string{"MONITOR EXTERNAL RESET EVENT #"},
		},
		{
			name:      "Type8",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 8)},
			wantTypes: []string{"MONITOR REMOTE RESET EVENT #"},
		},
		{
			name:      "Default",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 0)},
			wantTypes: []string{"MONITOR RESET EVENT #"},
		},
		{
			name: "Multiple",
			recs: [][]byte{
				makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1),
				makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 4),
			},
			wantTypes: []string{
				"MONITOR MANUAL RESET EVENT #",
				"MONITOR EXTERNAL RESET EVENT #",
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := buildMsgMonitorReset(len(tc.recs), tc.recs)

			switch tc.name {
			case "BadLength":
				// drop last byte to trigger length error
				msg = msg[:len(msg)-1]
			case "BadChecksum":
				// flip checksum
				msg[len(msg)-1] ^= 0xFF
			case "TooShort":
				msg = make([]byte, 6)
			}

			out, err := dev.LogMonitorReset(httpHdr, msg, hdr)
			if tc.wantErr != nil {
				if err == nil || !errors.Is(err, tc.wantErr) {
					t.Fatalf("expected %v, got %v", tc.wantErr, err)
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if len(out.Records) != len(tc.wantTypes) {
				t.Fatalf("got %d records, want %d", len(out.Records), len(tc.wantTypes))
			}
			for i, rec := range out.Records {
				if rec.ResetType != tc.wantTypes[i] {
					t.Errorf("record %d ResetType = %q; want %q", i, rec.ResetType, tc.wantTypes[i])
				}
				if !rec.DateTime.Equal(wantTime) {
					t.Errorf("record %d DateTime = %v; want %v", i, rec.DateTime, wantTime)
				}
			}
		})
	}
}

// computeChecksum returns the 1's-complement of the low byte of the sum of all but the last byte.
func computeChecksum(msg []byte) byte {
	var sum byte
	// sum all of the data (all but the last byte)
	for _, b := range msg[:len(msg)-1] {
		sum += b
	}
	// one's-complement so that sum(data)+checksum == 0xFF
	return ^sum
}

// brute-force a valid checksum by trying all possible last-byte values
func computeValidChecksum(t *testing.T, msg []byte) {
	for b := 0; b < 256; b++ {
		msg[len(msg)-1] = byte(b)
		if err := helper.ValidateChecksum(msg); err == nil {
			return
		}
	}
	t.Fatal("unable to find a valid checksum for test message")
}

func buildMsgPrevFail(nRecs int, modify func(rec []byte)) []byte {
	const hdrLen = 7
	const recSize = 98
	length := hdrLen + nRecs*recSize + 2
	msg := make([]byte, length)
	msg[7] = byte(nRecs)
	start := hdrLen + 1
	for i := 0; i < nRecs; i++ {
		rec := msg[start : start+recSize]
		// default zeros; let caller tweak
		if modify != nil {
			modify(rec)
		}
		start += recSize
	}
	// final checksum
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func TestLogPreviousFail(t *testing.T) {
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	baseHeader := &helper.HeaderRecord{Volt220: false, Model: helper.Mmu16le}

	tests := []struct {
		name      string
		header    *helper.HeaderRecord
		msg       []byte
		wantErr   error
		assertRec func(t *testing.T, rec *helper.LogPreviousFailRecords)
	}{
		{
			name:    "TooShort",
			header:  baseHeader,
			msg:     make([]byte, 6),
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:    "BadLength",
			header:  baseHeader,
			msg:     make([]byte, 10),
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:   "BadChecksum",
			header: baseHeader,
			msg: func() []byte {
				m := buildMsgPrevFail(1, nil)
				// corrupt the checksum
				m[len(m)-1] ^= 0xFF
				return m
			}(),
			wantErr: helper.ErrMsgByteChecksum,
		},
		// BCD conversion errors
		{
			name:   "InvalidMonthBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x1A, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name:   "InvalidDayBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x32, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name:   "InvalidHourBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x25, 0x28, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name:   "InvalidMinBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x61, 0x05, 0x28, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name:   "InvalidSecBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x61, 0x16, 0x05, 0x28, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
		{
			name:   "Basic",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				if out.DeviceModel != baseHeader.Model.String() {
					t.Errorf("DeviceModel = %q; want %q", out.DeviceModel, baseHeader.Model.String())
				}
				if len(out.Records) != 1 {
					t.Fatalf("got %d records; want 1", len(out.Records))
				}
				r := out.Records[0]

				// default fault (case 0 -> default branch)
				if r.Fault != "undefined fault type error" {
					t.Errorf("Fault = %q; want undefined fault type error", r.Fault)
				}
				// showFaultStatus==true for default -> FaultStatus slice length = j+1 = 12
				if len(r.FaultStatus) != 12 {
					t.Errorf("FaultStatus len = %d; want 12", len(r.FaultStatus))
				}
				// channel reds, yellows, greens always 12; walks too because type16Mode=false
				if len(r.ChannelRedStatus) != 12 || len(r.ChannelWalkStatus) != 12 {
					t.Errorf("ChannelStatus lens = R:%d W:%d; want 12,12",
						len(r.ChannelRedStatus), len(r.ChannelWalkStatus))
				}
				// fault!=64 -> no NextConflictingChannels
				if len(r.NextConflictingChannels) != 0 {
					t.Errorf("NextConflictingChannels = %d; want 0", len(r.NextConflictingChannels))
				}
				// no volt220, default acLine=0 Vrms @ 0Hz
				if r.AcLine != "0 Vrms @ 0Hz" {
					t.Errorf("AcLine = %q; want 0 Vrms @ 0Hz", r.AcLine)
				}
				// controlStatus bit7=0 -> redEnable Off(0)
				if r.RedEnable != "Off (0 Vrms)" {
					t.Errorf("RedEnable = %q; want Off (0 Vrms)", r.RedEnable)
				}
				// no lsFlashBit
				if r.LsFlashBit {
					t.Error("LsFlashBit = true; want false")
				}
				// temperature raw byte=0 -> 0-40 == -40
				if r.Temperature != -40 {
					t.Errorf("Temperature = %d; want -40", r.Temperature)
				}
			},
		},
		{
			name:   "FaultStatusBits",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// set faultStatus = 0x0009 -> bits 0 and 3
				// the app uses raw hex decimals as the date values
				rec[1] = 0x09  // low byte
				rec[2] = 0x00  // high byte
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// we expect 12 entries (j = 11 -> 0..11)
				if len(r.FaultStatus) != 12 {
					t.Fatalf("FaultStatus length = %d; want 12", len(r.FaultStatus))
				}
				// bit-0 and bit-3 should be true
				if !r.FaultStatus[0] || !r.FaultStatus[3] {
					t.Errorf("FaultStatus bits = %v; want index 0 and 3 true", r.FaultStatus)
				}
				// and verify a bit we didn't set remains false
				if r.FaultStatus[1] {
					t.Errorf("FaultStatus[1] = true; want false")
				}
			},
		},
		{
			name:   "ChannelStatusBits",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// rec[23..24] = red; turn on bit 2
				rec[23] = 0x04
				rec[24] = 0x00
				// rec[21..22] = yellow; turn on bit 1
				rec[21] = 0x02
				rec[22] = 0x00
				// rec[19..20] = green; turn on bit 0
				rec[19] = 0x01
				rec[20] = 0x00
				// rec[25..26] = walk; turn on bit 3
				rec[25] = 0x08
				rec[26] = 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelRedStatus[2] {
					t.Errorf("ChannelRedStatus[2]=false; want true")
				}
				if !r.ChannelYellowStatus[1] {
					t.Errorf("ChannelYellowStatus[1]=false; want true")
				}
				if !r.ChannelGreenStatus[0] {
					t.Errorf("ChannelGreenStatus[0]=false; want true")
				}
				if !r.ChannelWalkStatus[3] {
					t.Errorf("ChannelWalkStatus[3]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// enable the "type 12 w/ sdlc" bit
				rec[27] |= 0x04

				// fcStatusG -> rec[3..4] big-endian -> set bit 11 (0x0800)
				rec[3], rec[4] = 0x00, 0x08

				// fcStatusY -> rec[6..5] -> set bit 1 (0x0002)
				rec[6], rec[5] = 0x00, 0x02

				// fcStatusR -> rec[8..7] -> set bit 0 (0x0001)
				rec[8], rec[7] = 0x00, 0x01

				// leave all the other status words at 0

				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// Red field-check bit 0
				if !r.ChannelRedFieldCheckStatus[0] {
					t.Errorf("RedFieldCheck[0]=false; want true")
				}
				// Yellow field-check bit 1
				if !r.ChannelYellowFieldCheckStatus[1] {
					t.Errorf("YellowFieldCheck[1]=false; want true")
				}
				// Green field-check bit 11 (we masked &0xfff, but bit 11 remains)
				if !r.ChannelGreenFieldCheckStatus[11] {
					t.Errorf("GreenFieldCheck[11]=false; want true")
				}
				// with TYPE-12 + only G-FC bits set, we still get an array of 12 walk-FC flags (all false)
				if len(r.ChannelWalkFieldCheckStatus) != 12 {
					t.Errorf("ChannelWalkFieldCheckStatus length = %d; want 12", len(r.ChannelWalkFieldCheckStatus))
				}
			},
		},
		{
			name:   "RecurrentPulse_and_WalkRP_Status",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// rpStatusR -> rec[13..14] -> set bit 1
				rec[14], rec[13] = 0x00, 0x02
				// rpStatusY -> rec[11..12] -> set bit 2
				rec[12], rec[11] = 0x00, 0x04
				// rpStatusG -> rec[9..10] -> set bit 3
				rec[10], rec[9] = 0x00, 0x08
				// rpStatusW -> rec[15..16] -> set bit 4
				rec[16], rec[15] = 0x00, 0x10
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// Red RP bit 1
				if !r.ChannelRedRecurrentPulseStatus[1] {
					t.Errorf("RedRP[1]=false; want true")
				}
				// Yellow RP bit 2
				if !r.ChannelYellowRecurrentPulseStatus[2] {
					t.Errorf("YellowRP[2]=false; want true")
				}
				// Green RP bit 3
				if !r.ChannelGreenRecurrentPulseStatus[3] {
					t.Errorf("GreenRP[3]=false; want true")
				}
				// Walk RP bit 4 (type16Mode==false)
				if !r.ChannelWalkRecurrentPulseStatus[4] {
					t.Errorf("WalkRP[4]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12_Walk6",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// enable the "type 12 w/ sdlc" bit
				rec[27] |= 0x04
				// fcStatusG bit10 -> 0x0400 -> walkFcStatus |= 0x20 (bit index 5)
				rec[4], rec[3] = 0x04, 0x00
				// force fcStatusR non-zero so we populate walk-FC
				rec[7], rec[8] = 0x01, 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelWalkFieldCheckStatus[5] {
					t.Errorf("WalkFieldCheck[5]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12_Walk4",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[27] |= 0x04
				// fcStatusG bit9 -> 0x0200 -> walkFcStatus |= 0x08 (bit index 3)
				rec[4], rec[3] = 0x02, 0x00
				// force fcStatusR non-zero so we populate walk-FC
				rec[7], rec[8] = 0x01, 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelWalkFieldCheckStatus[3] {
					t.Errorf("WalkFieldCheck[3]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12_Walk2",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[27] |= 0x04
				// fcStatusG bit8 -> 0x0100 -> walkFcStatus |= 0x02 (bit index 1)
				rec[4], rec[3] = 0x01, 0x00
				// force fcStatusR non-zero so we populate walk-FC
				rec[7], rec[8] = 0x01, 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelWalkFieldCheckStatus[1] {
					t.Errorf("WalkFieldCheck[1]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_WalkFcStatus_AllFalse",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// leave controlStatus bit2 == 0 so type16Mode=false (j=11)
				// set fcStatusR (bytes 7..8) to non-zero so we enter the field-check block:
				rec[7], rec[8] = 0x01, 0x00 // fcStatusR = 1
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// we should have exactly 12 walk-FC bits (0..11)
				if len(r.ChannelWalkFieldCheckStatus) != 12 {
					t.Fatalf("WalkFieldCheck len = %d; want 12", len(r.ChannelWalkFieldCheckStatus))
				}
				// and since walkFcStatus stayed 0, every bit must be false
				for i, v := range r.ChannelWalkFieldCheckStatus {
					if v {
						t.Errorf("WalkFieldCheck[%d]=true; want false", i)
					}
				}
			},
		},
		{
			name:   "LsFlashBit_False_Type16",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// set bit1 so type16Mode==true, leave bit6 clear => LsFlashBit=false
				rec[27] = 0x02
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if r.LsFlashBit {
					t.Errorf("LsFlashBit = true; want false")
				}
			},
		},
		{
			name: "Complex",
			header: func() *helper.HeaderRecord {
				h := *baseHeader
				h.Volt220 = true
				h.Model = helper.Mmu16le
				return &h
			}(),
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// fault = 64 -> case 64 path for next-conflicts
				rec[0] = 64
				// set some bits in ggfonStatus (bytes 17,18)
				rec[17], rec[18] = 0x05, 0x00
				// controlStatus at offset 27: bits 1(type16),6(lsFlash),7(redEnable)
				rec[27] = 0xC2 // 1100 0010
				// acLineFrequency @ offset 28
				rec[28] = 60
				// acLineVoltage @ offset 30
				rec[30] = 120
				// raw temp @ offset 31 -> 50 -> 50-40 == 10
				rec[31] = 50
				// redEnableRmsVoltage @ offset 39
				rec[39] = 10
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				if out.DeviceModel != baseHeader.Model.String() {
					t.Errorf("DeviceModel = %q; want %q", out.DeviceModel, baseHeader.Model.String())
				}
				if len(out.Records) != 1 {
					t.Fatalf("got %d records; want 1", len(out.Records))
				}
				r := out.Records[0]

				// case 64 -> "Clearance (Yellow + Red) Fault"
				if r.Fault != "Clearance (Yellow + Red) Fault" {
					t.Errorf("Fault = %q; want Clearance (Yellow + Red) Fault", r.Fault)
				}
				// default showFaultStatus=true -> len = j+1 = 16
				if len(r.FaultStatus) != 16 {
					t.Errorf("FaultStatus len = %d; want 16", len(r.FaultStatus))
				}
				// type16Mode=true -> j=15 -> RedStatus len=16, WalkStatus=0
				if len(r.ChannelRedStatus) != 16 || len(r.ChannelWalkStatus) != 0 {
					t.Errorf("ChannelRedStatus len=%d,WalkStatus len=%d; want 16,0",
						len(r.ChannelRedStatus), len(r.ChannelWalkStatus))
				}
				// fault==64 -> next-conflicts len=16
				if len(r.NextConflictingChannels) != 16 {
					t.Errorf("NextConflictingChannels len=%d; want 16",
						len(r.NextConflictingChannels))
				}
				// acLine: Volt220=true -> 120*2 @60
				if r.AcLine != "240 Vrms @ 60Hz" {
					t.Errorf("AcLine = %q; want 240 Vrms @ 60Hz", r.AcLine)
				}
				// redEnable bit7=1 -> Active; redEnableRms=10*2=20
				if r.RedEnable != "Active (20 Vrms)" {
					t.Errorf("RedEnable = %q; want Active (20 Vrms)", r.RedEnable)
				}
				// lsFlashBit bit6=1 -> true
				if !r.LsFlashBit {
					t.Error("LsFlashBit = false; want true")
				}
				// temperature 50−40=10
				if r.Temperature != 10 {
					t.Errorf("Temperature = %d; want 10", r.Temperature)
				}
			},
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			got, err := dev.LogPreviousFail(httpHdr, tc.msg, tc.header)
			if tc.wantErr != nil {
				if err == nil || err.Error()[:len(tc.wantErr.Error())] != tc.wantErr.Error() {
					t.Fatalf("expected error %v, got %v", tc.wantErr, err)
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			tc.assertRec(t, got)
		})
	}
}

// buildMsg builds a correctly-sized LogACLineEvent payload:
//
//	HeaderLength=7, count byte at 7, then N×8-byte records, then checksum.
func buildMsgLogACLineEvent(nRecs int, recs [][]byte) []byte {
	const HeaderLength = 7
	const RecordSize = 8

	totalLen := HeaderLength + 1 + nRecs*RecordSize + 1
	msg := make([]byte, totalLen)

	// record count at offset 7
	msg[7] = byte(nRecs)

	// copy in each 8-byte record, forcing first byte = 0x41
	start := HeaderLength + 1
	for _, r := range recs {
		if len(r) != RecordSize {
			panic("each record slice must be exactly 8 bytes")
		}
		r[0] = 0x41
		copy(msg[start:], r)
		start += RecordSize
	}

	// now compute and append the one's-complement checksum
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func TestLogACLineEvent(t *testing.T) {
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	base := &helper.HeaderRecord{
		Model:          helper.Mmu16le,
		MainsDC:        false,
		PowerDownLevel: 50,
		BlackoutLevel:  20,
	}

	makeRec := func(evType, lv, sec, min, hr, day, mon, yr byte) []byte {
		return []byte{evType, lv, sec, min, hr, day, mon, yr}
	}
	utcTime, _ := time.LoadLocation("UTC")

	type tc struct {
		name          string
		header        *helper.HeaderRecord
		recs          [][]byte
		corrupt       func(msg []byte) // if non-nil, applied after build
		wantErr       error
		wantVoltType  int64
		wantLineRms   int64
		wantEventType string
		wantDate      time.Time
	}
	tests := []tc{
		{
			name:         "Above blackout, AC mode",
			header:       base,
			recs:         [][]byte{makeRec(1, 25, 0x12, 0x34, 0x1, 0x2, 0x3, 0x21)},
			wantErr:      nil,
			wantVoltType: 1,
			wantLineRms:  25,
			wantEventType: helper.GetACLineEventType(
				base.Model, base.MainsDC, 25, base.PowerDownLevel, 0x41,
			),
			wantDate: time.Date(int(2021), time.Month(3), int(2), int(1), int(34), int(12), 0, utcTime),
		},
		{
			name: "Below blackout, DC mode",
			header: func() *helper.HeaderRecord {
				h := *base
				h.MainsDC = true
				h.BlackoutLevel = 30
				return &h
			}(),
			recs:         [][]byte{makeRec(2, 20, 0x5, 0x15, 0x10, 0x6, 0x7, 0x22)},
			wantErr:      nil,
			wantVoltType: 2,
			wantLineRms:  0, // 20 < blackout 30
			wantEventType: helper.GetACLineEventType(
				base.Model, true, 20, base.PowerDownLevel, 0x41,
			),
			wantDate: time.Date(int(2022), time.Month(7), int(6), int(10), int(15), int(5), 0, utcTime),
		},

		// ---- error cases ----
		{
			name:    "BadLength",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x5, 0x15, 0x10, 0x6, 0x7, 0x22)},
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:   "BadChecksum",
			header: base,
			recs:   [][]byte{makeRec(1, 25, 0x5, 0x15, 0x10, 0x6, 0x7, 0x22)},
			corrupt: func(msg []byte) {
				// flip the checksum byte
				msg[len(msg)-1] ^= 0xFF
			},
			wantErr: helper.ErrMsgByteChecksum,
		},
		{
			name:    "TooShort",
			header:  base,
			recs:    nil, // ignored
			wantErr: helper.ErrMsgByteLen,
		},

		{
			name:    "InvalidMonthBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x1A, 0x28, 0x05, 0x16, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name:    "InvalidDayBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x02, 0x05, 0x55, 0x05, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name:    "InvalidHourBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x02, 0x28, 0x25, 0x05, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name:    "InvalidMinBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x77, 0x12, 0x05, 0x07, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name:    "InvalidSecBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x80, 0x02, 0x12, 0x05, 0x05, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			var msg []byte
			switch tc.name {
			case "TooShort":
				msg = make([]byte, 6)
			case "BadLength":
				msg = buildMsgLogACLineEvent(len(tc.recs), tc.recs)
				msg = msg[:len(msg)-1]
			default:
				msg = buildMsgLogACLineEvent(len(tc.recs), tc.recs)
			}

			if tc.corrupt != nil {
				tc.corrupt(msg)
			}
			out, err := dev.LogACLineEvent(httpHdr, msg, tc.header)

			if tc.wantErr != nil {
				if err == nil {
					t.Fatalf("expected error %v, got nil", tc.wantErr)
				}
				if !errors.Is(err, tc.wantErr) {
					t.Fatalf("error = %v; want %v", err, tc.wantErr)
				}
				return
			}

			// success path
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if out.DeviceModel != base.Model.String() {
				t.Errorf("DeviceModel = %q; want %q", out.DeviceModel, base.Model.String())
			}
			if out.VoltageType != tc.wantVoltType {
				t.Errorf("VoltageType = %d; want %d", out.VoltageType, tc.wantVoltType)
			}
			if len(out.Records) != 1 {
				t.Fatalf("got %d records; want 1", len(out.Records))
			}
			rec := out.Records[0]
			if rec.LineVoltageRms != tc.wantLineRms {
				t.Errorf("LineVoltageRms = %d; want %d", rec.LineVoltageRms, tc.wantLineRms)
			}
			if rec.EventType != tc.wantEventType {
				t.Errorf("EventType = %v; want %v", rec.EventType, tc.wantEventType)
			}
			if !rec.DateTime.Equal(tc.wantDate) {
				t.Errorf("DateTime = %v; want %v", rec.DateTime, tc.wantDate)
			}
		})
	}
}

// build a minimal LogFaultSignalSequence message with N records and given faultType
func buildMsg(numRecords int, faultType byte) []byte {
	const headerLen = 7
	const traceLen = 12

	// total length = headerLen + numRecords*traceLen + 1(checksum)
	total := headerLen + 2 + numRecords*traceLen + 2
	msg := make([]byte, total)

	// byte 8 = faultType,  byte 9 = numberOfRecords
	msg[8] = faultType
	msg[9] = byte(numRecords)

	return msg
}

func TestLogFaultSignalSequence(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}
	// our parser only needs MaxChannels; CommVersion isn't used in this path
	headerDetail := &helper.HeaderRecord{MaxChannels: 2}

	tests := []struct {
		name          string
		build         func() []byte
		wantErr       bool
		wantFaultType string
	}{
		{
			name: "InvalidLength",
			build: func() []byte {
				return make([]byte, 5) // way too short
			},
			wantErr: true,
		},
		{
			name: "InvalidLength2",
			build: func() []byte {
				msg := make([]byte, 10) // too short
				msg[9] = 2
				return msg
			},
			wantErr: true,
		},
		{
			name: "BadChecksum",
			build: func() []byte {
				// correct length for 1 record, but leave checksum = 0
				msg := buildMsg(1, 1)
				// zero-out the whole trace
				for i := 10; i < len(msg)-1; i++ {
					msg[i] = 0
				}
				msg[len(msg)-1] = 0 // wrong checksum
				return msg
			},
			wantErr: true,
		},
		{
			name: "NormalTimestamp",
			build: func() []byte {
				msg := buildMsg(2, 1) // faultType=1 -> "CVM Fault"
				// set a "normal" timestamp of 100 (0x00,0x64) in the first two trace bytes
				msg[10] = 0x00
				msg[11] = 0x64
				// leave all other trace-status bytes at 0
				for i := 12; i < 10+12; i++ {
					msg[i] = 0
				}
				msg[23] = 0x00
				msg[24] = 0x64
				// leave all other trace-status bytes at 0
				for i := 25; i < 23+12; i++ {
					msg[i] = 0
				}
				computeValidChecksum(t, msg)
				return msg
			},
			wantErr:       false,
			wantFaultType: "CVM Fault",
		},
		{
			name: "RolloverTimestamp",
			build: func() []byte {
				msg := buildMsg(2, 1)
				// set timestamp = 0xFF,0xFF -> 65535 > 65530 so should be clamped
				msg[10] = 0xFF
				msg[11] = 0xFF
				for i := 12; i < 10+12; i++ {
					msg[i] = 0
				}
				msg[23] = 0xFF
				msg[24] = 0xFF
				// leave all other trace-status bytes at 0
				for i := 25; i < 23+12; i++ {
					msg[i] = 0
				}
				computeValidChecksum(t, msg)
				return msg
			},
			wantErr:       false,
			wantFaultType: "CVM Fault",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := tc.build()
			records, err := dev.LogFaultSignalSequence(hdr, msg, headerDetail)
			if tc.wantErr {
				if err == nil {
					t.Fatalf("expected error, got none")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			// we at least got a non-nil result with FaultType set
			if records.FaultType != tc.wantFaultType {
				t.Errorf("FaultType = %q; want %q", records.FaultType, tc.wantFaultType)
			}
			if len(records.Records) == 0 {
				t.Errorf("no trace records returned")
			}
		})
	}
}

func logConfigurationBaseHeader() *pubsubdata.HeaderDetails {
	return &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
}

func logConfigurationBaseHelperHeader() *helper.HeaderRecord {
	return &helper.HeaderRecord{MaxChannels: 16, FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29)}
}

func logConfigurationBuildValidMessage(recCount int) []byte {
	const (
		HeaderLength = 7
		RecordSize   = 64
	)
	// total length = 7 + recCount*64 + 1
	length := HeaderLength + recCount*RecordSize + 2
	msg := make([]byte, length)

	// numberOfRecords
	msg[7] = byte(recCount)

	// one record only: fill at offset = HeaderLength+1
	start := HeaderLength + 1

	// 1) conflict maps: make all zero to force permissives = ["2","3",...]
	for off := 0; off < 30; off += 2 {
		// two-byte words at start+off, start+off+1
		msg[start+off] = 0x00
		msg[start+off+1] = 0x00
	}

	// 2) mycd/myrcd = 0x0000 to test true; fieldCheckG/Y/R, dualEnableYR/GR/GY, redFailEnable = 0xFFFF to test true
	offs := []int{30, 32, 34, 36, 38, 40, 42, 44, 46}
	for _, off := range offs {
		// words at off, off+1
		v := uint16(0xFFFF)
		msg[start+off] = byte(v & 0xff)
		msg[start+off+1] = byte(v >> 8)
	}
	// but mycd/myrcd are at off=30,32: override to zero
	msg[start+30] = 0
	msg[start+31] = 0
	msg[start+32] = 0
	msg[start+33] = 0

	// 3) vmMinFlash, options1, options2, fyaEnable1, fyaEnable2, selectJumpers1
	msg[start+48] = 0x2C // 0010_1100
	msg[start+49] = 0xF3 // 1111_0011
	msg[start+50] = 0x9A // 1001_1010
	msg[start+51] = 0x00 // low nibble => Mode A branch
	msg[start+52] = 0x10 // trap_mode only => test FYA trap bit
	msg[start+53] = 0x07 // 0000_0111 => legacy bits for legacy test

	// 4) BCD timestamp: sec, min, hr, day, mon, yr
	msg[start+55] = 0x00 // seconds
	msg[start+56] = 0x00 // minutes
	msg[start+57] = 0x00 // hours
	msg[start+58] = 0x02 // day = 2
	msg[start+59] = 0x01 // month = Jan
	msg[start+60] = 0x21 // year = '21 => 2021

	// 5) configChangeSource
	msg[start+61] = 3 // Program Card Entry

	// 6) crc low/high at offsets 62/63
	msg[start+62] = 0x0A
	msg[start+63] = 0x00

	// now checksum of whole message
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func TestLogConfiguration_AllBranches(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}

	hdrModern := logConfigurationBaseHeader()
	// helperHdr := logConfigurationBaseHelperHeader()
	// set CommVersion>=0x38, FirmwareRevision>0x73, FirmwareVersion>0x28 for modern-FYA high-Ver branches
	hdrModern.GatewayTimezone = "UTC"
	hh := &helper.HeaderRecord{
		CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
		FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74),
		FirmwareVersion:  helper.ConvertByteToString(0x29),
		MaxChannels:      16,
		Model:            helper.Mmu16le,
	}

	msg := logConfigurationBuildValidMessage(1)
	rec, err := dev.LogConfiguration(hdrModern, msg, hh)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if rec.DeviceModel != hh.Model.String() {
		t.Errorf("DeviceModel = %q; want %q", rec.DeviceModel, hh.Model.String())
	}
	if len(rec.Record) != 1 {
		t.Fatalf("got %d records; want 1", len(rec.Record))
	}
	c := rec.Record[0]

	// DateTime
	wantDT := time.Date(2021, 1, 2, 0, 0, 0, 0, time.UTC)
	if !c.DateTime.Equal(wantDT) {
		t.Errorf("DateTime = %v; want %v", c.DateTime, wantDT)
	}

	// permissives length
	if len(c.Ch01Permissives) != 15 {
		t.Errorf("Ch01Permissives len = %d; want 15", len(c.Ch01Permissives))
	}

	// mycd=0 => all true; myrcd=0 => all true
	for i, v := range c.MinimumYellowClearanceEnable {
		if !v {
			t.Errorf("MinYellowClear[%d]=false; want true", i)
		}
	}
	for i, v := range c.MinimumYellowRedClearanceEnable {
		if !v {
			t.Errorf("MinYellowRedClear[%d]=false; want true", i)
		}
	}

	// field checks & dual & redFail all true
	for i := 0; i < 16; i++ {
		if !c.FieldCheckEnableRed[i] ||
			!c.FieldCheckEnableYellow[i] ||
			!c.FieldCheckEnableGreen[i] ||
			!c.GreenRedDualEnable[i] ||
			!c.YellowRedDualEnable[i] ||
			!c.GreenYellowDualEnable[i] ||
			!c.RedFailEnable[i] {
			t.Errorf("bit %d: expected all true flags", i)
		}
	}

	// options1 flags
	if c.RecurrentPulse {
		t.Error("RecurrentPulse = true; want false")
	}
	if !c.WatchdogEnableSwitch {
		t.Error("WatchdogEnableSwitch = false; want true")
	}
	if !c.WalkEnableTs1 {
		t.Error("WalkEnableTs1 = false; want true")
	}
	if c.X24VIiInputThreshold != "12 Vdc" {
		t.Errorf("X24VIiInputThreshold = %q; want 12 Vdc", c.X24VIiInputThreshold)
	}
	if !c.LogCvmFaults {
		t.Error("LogCvmFaults = false; want true")
	}
	if !c.ProgramCardMemory {
		t.Error("ProgramCardMemory = false; want true")
	}
	if !c.LEDguardThresholds {
		t.Error("LEDguardThresholds = false; want true")
	}

	// options2 flags
	if !c.ForceType_16Mode {
		t.Error("ForceType_16Mode = false; want true")
	}
	if c.Type_12WithSdlcMode {
		t.Error("Type_12WithSdlcMode = true; want false")
	}
	if c.VmCvm_24V_3XdayLatch {
		t.Error("VmCvm_24V_3XdayLatch = true; want false")
	}

	// vmMinFlash => "13 seconds"
	if c.MinimumFlashTime != "13 seconds" {
		t.Errorf("MinimumFlashTime = %q; want 13 seconds", c.MinimumFlashTime)
	}
	if !c.CvmLatchEnable {
		t.Error("CvmLatchEnable = false; want true")
	}
	if c.X24VLatchEnable {
		t.Error("X24VLatchEnable = true; want false")
	}

	// selectJumpers1 => bits 0,1,2 => all true => Inhibit & Port & TypeMode="16"
	if !c.X24VoltInhibit {
		t.Error("X24VoltInhibit = false; want true")
	}
	if !c.Port_1Disable {
		t.Error("Port_1Disable = false; want true")
	}
	if c.TypeMode != "16" {
		t.Errorf("TypeMode = %q; want 16", c.TypeMode)
	}

	// modern FYA, no remap => FlashingYellowArrows="Mode A, Channel Pairs: <none>"
	if got := c.FlashingYellowArrows[0]; got != "Mode A, Channel Pairs: <none>" {
		t.Errorf("FlashingYellowArrows = %q; want Mode A, Channel Pairs: <none>", got)
	}
	if c.FyaRedAndYellowEnable != "Channels: <none>" {
		t.Errorf("FyaRedAndYellowEnable = %q; want Channels: <none>", c.FyaRedAndYellowEnable)
	}
	if !c.FyaYellowTrapDetection {
		t.Error("FyaYellowTrapDetection = false; want true")
	}
	if c.FyaFlashRateDetection {
		t.Error("FyaFlashRateDetection = true; want false")
	}

	// PLT5 suppression: options2&0x8 => ball suppression
	if c.Pplt5Suppression != "PPLT5 Ball Suppression = " {
		t.Errorf("Pplt5Suppression = %q; want PPLT5 Ball Suppression = ", c.Pplt5Suppression)
	}

	// source & CRC
	if c.ChangeSource != "Program Card Entry" {
		t.Errorf("ChangeSource = %q; want Program Card Entry", c.ChangeSource)
	}
	if c.CheckValue != "10" {
		t.Errorf("CheckValue = %q; want 10", c.CheckValue)
	}
}

func TestLogConfiguration_Errors(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := logConfigurationBaseHeader()
	hh := logConfigurationBaseHelperHeader()

	t.Run("short length", func(t *testing.T) {
		t.Parallel()
		_, err := dev.LogConfiguration(hdr, make([]byte, 10), hh)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("err = %v; want helper.ErrMsgByteLen", err)
		}
	})

	t.Run("bad checksum", func(t *testing.T) {
		t.Parallel()
		msg := logConfigurationBuildValidMessage(1)
		// break checksum
		msg[len(msg)-1] ^= 0xFF
		_, err := dev.LogConfiguration(hdr, msg, hh)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want helper.ErrMsgByteChecksum", err)
		}
	})
}

func TestLogConfiguration_LegacyFYA(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := logConfigurationBaseHeader()
	hh := &helper.HeaderRecord{
		CommVersion:     helper.ConvertByteToDecimalFormat(0x30), // <0x38 => legacy path
		FirmwareVersion: helper.ConvertByteToDecimalFormat(0x20),
		MaxChannels:     16,
	}

	msg := logConfigurationBuildValidMessage(1)
	// clear selectJumpers1 bits so FlashingYellowArrows="<none>"
	msg[7+1+53] = 0x00
	msg[len(msg)-1] = computeChecksum(msg)

	rec, err := dev.LogConfiguration(hdr, msg, hh)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	c := rec.Record[0]
	if got := c.FlashingYellowArrows[0]; got != "<none>" {
		t.Errorf("legacy FlashingYellowArrows = %q; want <none>", got)
	}
}

func buildBaseConfigMsg(recCount int, modify func(rec []byte)) []byte {
	const (
		hdrLen  = 7
		recSize = 64
	)
	length := hdrLen + recCount*recSize + 2
	msg := make([]byte, length)
	// set record count
	msg[7] = byte(recCount)
	// prepare record area
	start := hdrLen + 1
	for i := 0; i < recCount; i++ {
		rec := msg[start : start+recSize]
		// zero init, then apply modifier
		if modify != nil {
			modify(rec)
		}
		start += recSize
	}
	// append checksum
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func TestLogConfiguration_BCDErrors(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	hh := &helper.HeaderRecord{MaxChannels: 16, FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29), FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), CommVersion: helper.ConvertByteToDecimalFormat(0x38)}

	tests := []struct {
		name    string
		modify  func(rec []byte)
		wantErr error
	}{
		{
			name: "InvalidMonthBCD",
			modify: func(rec []byte) {
				// month at offset 59 -> invalid BCD 0x1A
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x00, 0x02, 0x1A, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name: "InvalidDayBCD",
			modify: func(rec []byte) {
				// day at offset 58 -> invalid BCD 0x32 (>31)
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x00, 0x32, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name: "InvalidHourBCD",
			modify: func(rec []byte) {
				// hour at offset 57 -> invalid BCD 0x25 (>23)
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x25, 0x02, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name: "InvalidMinBCD",
			modify: func(rec []byte) {
				// minute at offset 56 -> invalid BCD 0x61
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x61, 0x00, 0x02, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name: "InvalidSecBCD",
			modify: func(rec []byte) {
				// second at offset 55 -> invalid BCD 0x61
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x61, 0x00, 0x00, 0x02, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := buildBaseConfigMsg(1, tc.modify)
			// invoke
			_, err := dev.LogConfiguration(hdr, msg, hh)
			if !errors.Is(err, tc.wantErr) {
				t.Fatalf("%s: expected %v, got %v", tc.name, tc.wantErr, err)
			}
		})
	}
}

func TestLogConfiguration_AdditionalBranches(t *testing.T) {
	dev := EDIMMU216LE{}
	baseHdr := logConfigurationBaseHeader()

	type sub struct {
		name       string
		modifyMsg  func([]byte)
		modifyHH   func(*helper.HeaderRecord)
		assertFunc func(*testing.T, helper.ConfigurationChangeLogRecord)
	}

	subtests := []sub{
		{
			name: "X24VThreshold_24Vdc",
			// clear bit0 of options1 (offset 49)
			modifyMsg: func(msg []byte) {
				msg[7+1+49] &^= 0x1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				if c.X24VIiInputThreshold != "24 Vdc" {
					t.Errorf("Threshold = %q; want 24 Vdc", c.X24VIiInputThreshold)
				}
			},
		},
		{
			name: "FYA_Remap_ModeE_And_Pairs",
			// turn on remap bit in fyaEnable2 (offset 52) and set fyaEnable1 to 0xD1 to hit Mode E + low-nibble=1
			modifyMsg: func(msg []byte) {
				msg[7+1+51] = 0xD1 // fyaEnable1
				msg[7+1+52] = 0x40 // fyaEnable2 remap bit
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				got := c.FlashingYellowArrows[0]
				if got != "Mode E, Channel Pairs: 1-9" {
					t.Errorf("FYA remap = %q; want Mode E, Channel Pairs: 1-9", got)
				}
			},
		},
		{
			name: "Pplt5_ArrowSuppressionElse",
			// clear bit3 of options2 (offset 50) so we go into Arrow Suppression = branch
			modifyMsg: func(msg []byte) {
				msg[7+1+50] = 0x03
				msg[len(msg)-1] = computeChecksum(msg)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				if c.Pplt5Suppression != "PPLT5 Arrow Suppression = <none>" {
					t.Errorf("Pplt5Suppression = %q; want PPLT5 Arrow Suppression = <none>", c.Pplt5Suppression)
				}
			},
		},
		{
			name: "Pplt5_Suppression_Jumpers_12Mode",
			// legacy-FYA path cleanup: force selectJumpers1 bits for <0x38 CommVersion>
			modifyMsg: func(msg []byte) {
				msg[7+1+53] = 0xE1 // bits 1-4 => 0001_1110
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x37)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				// 0x1E => suppress 2,3,4,5 pairs => "3-10","5-11","7-12" in legacy 12-mode
				if !strings.Contains(c.Pplt5Suppression, "3-10") ||
					!strings.Contains(c.Pplt5Suppression, "5-11") ||
					!strings.Contains(c.Pplt5Suppression, "7-12") {
					t.Errorf("legacy Pplt5Suppression = %q; want channels 3-10,5-11,7-12", c.Pplt5Suppression)
				}
			},
		},
		{
			name: "ConfigChangeSource_Cases",
			modifyMsg: func(msg []byte) {
				// we'll iterate sources 1,2,4, and 0
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				// no-op, tested inside loop below
			},
		},
		{
			name: "FYA_Remap_ModeF_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 ← fya_msb_hi + fya_srcY + fya_fYa_lo + fya_ry_lo = 0x20+0x40+0x80+0x10 = 0xF0
				msg[7+1+51] = 0xF0 // fyaEnable1
				msg[7+1+52] = 0x40 // fyaEnable2 remap bit
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode F, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Remap_ModeI_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 ← fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50
				msg[7+1+51] = 0x50
				msg[7+1+52] = 0x40 // remap
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode I, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Remap_ModeJ_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 ← fya_msb_hi + fya_srcY + fya_ry_lo = 0x20+0x40+0x10 = 0x70
				msg[7+1+51] = 0x70
				msg[7+1+52] = 0x40
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode J, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Remap_ModeError_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 = 0x00 (no valid high-nibble), still remap bit set
				msg[7+1+51] = 0x00
				msg[7+1+52] = 0x40
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode <error>, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeB_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi = 0x20
				msg[7+1+51] = 0x20
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeC_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_fYa_lo + fya_ry_lo = 0x80 + 0x10 = 0x90
				msg[7+1+51] = 0x90
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode C, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeD_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi + fya_fYa_lo + fya_ry_lo = 0x20+0x80+0x10 = 0xB0
				msg[7+1+51] = 0xB0
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode D, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeG_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50
				msg[7+1+51] = 0x50
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode G, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeH_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi + fya_srcY + fya_ry_lo = 0x20+0x40+0x10 = 0x70
				msg[7+1+51] = 0x70
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode H, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeK_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_ry_lo = 0x10
				msg[7+1+51] = 0x10
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode K, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeL_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi + fya_ry_lo = 0x20+0x10 = 0x30
				msg[7+1+51] = 0x30
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode L, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Default_ModeError",
			modifyMsg: func(msg []byte) {
				// high-nibble = 0xC0 triggers the default case, low-nibble=0 so no channel pairs
				msg[7+1+51] = 0xC0 // fyaEnable1
				// leave fyaEnable2 at 0 (no remap)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // enable the new-style FYA logic
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode <error>, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Default_ModeError_LegacyFYA",
			modifyMsg: func(msg []byte) {
				// high-nibble = 0xC0 triggers the default case, low-nibble=0 so no channel pairs
				msg[7+1+51] = 0xC0 // fyaEnable1
				// leave fyaEnable2 at 0 (no remap)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // enable new-style FYA logic
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode <error>, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeA_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 = 0x00 for Mode A (high nibble = 0)
				msg[7+1+51] = 0x00
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode A, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_2",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_3",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_4",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_5",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x73) // ≤0x73 -> else-branch
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_6",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)      // so we take the new-style FYA logic
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x73) // ≤0x73 so we hit the "else" and get Channels: <none>
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_1",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_2",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_3",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_4",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_1",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_2",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_3",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_4",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
	}

	for _, st := range subtests {
		t.Run(st.name, func(t *testing.T) {
			hdr := baseHdr
			hh := logConfigurationBaseHelperHeader()
			if st.modifyHH != nil {
				st.modifyHH(hh)
			}
			msg := logConfigurationBuildValidMessage(1)
			if st.modifyMsg != nil {
				st.modifyMsg(msg)
			}
			rec, err := dev.LogConfiguration(hdr, msg, hh)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			c := rec.Record[0]
			st.assertFunc(t, c)
		})
	}

	// now ConfigChangeSource 1,2,4,0
	for _, src := range []byte{1, 2, 4, 0} {
		t.Run(fmt.Sprintf("Source%d", src), func(t *testing.T) {
			t.Parallel()
			hdr := baseHdr
			hh := logConfigurationBaseHelperHeader()
			msg := logConfigurationBuildValidMessage(1)
			msg[7+1+61] = src
			msg[len(msg)-1] = computeChecksum(msg)
			rec, err := dev.LogConfiguration(hdr, msg, hh)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			want := map[byte]string{
				1: "Front Panel Entry",
				2: "ECcom Download",
				3: "Program Card Entry",
				4: "Front Panel 'Set Default' Entry",
				0: "",
			}[src]
			if got := rec.Record[0].ChangeSource; got != want {
				t.Errorf("ChangeSource for %d = %q; want %q", src, got, want)
			}
		})
	}
}

func TestMonitorIDandName(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}                       // not used by MonitorIDandName
	helperHdr := &helper.HeaderRecord{Model: helper.Mmu16le} // not used by MonitorIDandName

	t.Run("success", func(t *testing.T) {
		t.Parallel()
		// build a 7+30+1=38 byte message
		msg := make([]byte, 38)
		// arbitrary header bytes [0..4]
		for i := 0; i < 5; i++ {
			msg[i] = byte(0x10 + i)
		}
		// monitor ID = 0x0102
		msg[5] = 0x02 // LS
		msg[6] = 0x01 // MS
		// fill name starting at offset 7 with "TestName"
		copy(msg[7:], []byte("TestName"))
		// leave the rest zero
		// compute and write checksum at last byte
		msg[len(msg)-1] = computeChecksum(msg)

		mon, err := dev.MonitorIDandName(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		if mon.DeviceModel != helper.Mmu16le.String() {
			t.Errorf("DeviceModel = %q; want %q", mon.DeviceModel, helper.Mmu16le.String())
		}
		if mon.MonitorId != 0x0102 {
			t.Errorf("MonitorId = %d; want 0x0102", mon.MonitorId)
		}
		if mon.MonitorName != "TestName" {
			t.Errorf("MonitorName = %q; want %q", mon.MonitorName, "TestName")
		}
	})

	t.Run("error: length too short", func(t *testing.T) {
		t.Parallel()
		short := make([]byte, 37)
		_, err := dev.MonitorIDandName(hdr, short, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteLen", err)
		}
	})

	t.Run("error: bad checksum", func(t *testing.T) {
		t.Parallel()
		// build correct-length but leave bad checksum
		msg := make([]byte, 38)
		msg[5], msg[6] = 1, 0
		msg[7] = 'X' // a printable
		// bad checksum:
		msg[len(msg)-1] = 0xFF
		_, err := dev.MonitorIDandName(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteChecksum", err)
		}
	})
}

func TestRMSEngineData(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}                       // not used
	helperHdr := &helper.HeaderRecord{Model: helper.Mmu16le} // not used

	buildValid := func() []byte {
		msg := make([]byte, 20)
		// pick header bytes 0..7
		for i := 0; i < 8; i++ {
			msg[i] = byte(0x11 + i)
		}

		// filler at idx 8
		msg[8] = 0x99
		// compute version checksum at idx 9
		msg[9] = computeChecksum(msg[:10])

		// revision preamble at idx 10
		msg[10] = 0x00
		// copy versionHeader (idx1..6) into revisionHeader slots idx11..16
		copy(msg[11:17], msg[1:7])

		// filler at idx 18
		msg[18] = 0x77
		// compute revision checksum at idx 18 over msg[10..19]
		msg[19] = computeChecksum(msg[10:20])

		return msg
	}

	t.Run("success", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		r, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		if r.DeviceModel != helper.Mmu16le.String() {
			t.Errorf("DeviceModel = %q; want %q", r.DeviceModel, helper.Mmu16le.String())
		}
		// note: the code (quirk!) sets EngineVersion = byteMsg[8], EngineRevision = byteMsg[18]
		if r.EngineVersion != helper.ConvertByteToString(msg[8]) {
			t.Errorf("EngineVersion = %s; want %s", r.EngineVersion, helper.ConvertByteToString(msg[8]))
		}
		if r.EngineRevision != helper.ConvertByteToDecimalFormat(msg[18]) {
			t.Errorf("EngineRevision = %s; want %s", r.EngineRevision, helper.ConvertByteToDecimalFormat(msg[18]))
		}
	})

	t.Run("error: length", func(t *testing.T) {
		t.Parallel()
		_, err := dev.RMSEngineData(hdr, []byte{0x00}, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteLen", err)
		}
	})

	t.Run("error: bad version checksum", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		msg[8] ^= 0xFF // break version checksum
		_, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteChecksum", err)
		}
	})

	t.Run("error: bad revision checksum", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		msg[18] ^= 0xFF // break revision checksum
		_, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteChecksum", err)
		}
	})

	t.Run("error: header mismatch", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		// tweak one byte of the revision header so versionHeader != revisionHeader
		msg[11] ^= 0xFF
		// recalc revision checksum
		msg[19] = computeChecksum(msg[10:20])
		_, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrRMSEngineDataHeaderMismatch) {
			t.Errorf("err = %v; want helper.ErrRMSEngineDataHeaderMismatch", err)
		}
	})
}

// baseMsgRMSStatus returns a 106-byte slice pre-filled with valid defaults for RMSStatus
func baseMsgRMSStatus() []byte {
	msg := make([]byte, 106)
	// temperature at byte 38: raw=50 => 10°F
	msg[38] = 50
	// channel statuses: green bits 0-1 set (MaxChannels=2)
	msg[26] = 0x03 // low byte
	msg[27] = 0x00 // high byte
	// yellow & red statuses = 0
	msg[28], msg[29] = 0, 0
	msg[30], msg[31] = 0, 0
	// BCD date/time parts: sec, min, hr, day, mon, yr
	msg[39] = 0x18 // sec = 18
	msg[40] = 0x16 // min = 16
	msg[41] = 0x09 // hr  = 9
	msg[42] = 0x08 // day = 8
	msg[43] = 0x07 // mon = 7
	msg[44] = 0x21 // yr  = 21 -> 2021
	// voltages: green=1, yellow=2, red=3
	for i := 47; i <= 62; i++ {
		msg[i] = 1
	}
	for i := 63; i <= 78; i++ {
		msg[i] = 2
	}
	for i := 79; i <= 94; i++ {
		msg[i] = 3
	}
	return msg
}

func TestRMSStatus_Errors(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2}

	// 1) length error
	if _, err := d.RMSStatus(hdr, make([]byte, 105), helperHdr); err == nil {
		t.Errorf("expected length error for len<106, got nil")
	}

	// 2) checksum error
	msg := baseMsgRMSStatus()
	msg[len(msg)-1] = 0x00 // wrong checksum
	if _, err := d.RMSStatus(hdr, msg, helperHdr); !errors.Is(err, helper.ErrMsgByteChecksum) {
		t.Errorf("expected checksum error, got %v", err)
	}

	// 3) date parts validation error: invalid month (0x13 => 19)
	msg = baseMsgRMSStatus()
	// set BCD month to 0x13 (invalid)
	msg[43] = 0x13
	msg[len(msg)-1] = computeChecksum(msg)
	if _, err := d.RMSStatus(hdr, msg, helperHdr); !errors.Is(err, helper.ErrValidateDateTimePartsMon) {
		t.Errorf("expected date parts validation error, got %v", err)
	}
}

func TestRMSStatus_NoFault(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2, Volt220: false}

	msg := baseMsgRMSStatus()
	// set fault = 0 => no fault
	msg[7] = 0x00
	msg[len(msg)-1] = computeChecksum(msg)

	rec, err := d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	// no fault
	if rec.IsFaulted {
		t.Error("expected IsFaulted=false for fault=0")
	}
	if rec.Fault != "No Fault" {
		t.Errorf("expected Fault='No Fault', got '%s'", rec.Fault)
	}
	// channel statuses
	if len(rec.ChannelGreenStatus) != 2 || !rec.ChannelGreenStatus[0] || !rec.ChannelGreenStatus[1] {
		t.Error("expected both green channels true")
	}
	if len(rec.ChannelYellowStatus) != 2 || rec.ChannelYellowStatus[0] || rec.ChannelYellowStatus[1] {
		t.Error("expected both yellow channels false")
	}
	if len(rec.ChannelRedStatus) != 2 || rec.ChannelRedStatus[0] || rec.ChannelRedStatus[1] {
		t.Error("expected both red channels false")
	}
	// temperature
	if rec.Temperature != 10 {
		t.Errorf("expected Temperature=10, got %d", rec.Temperature)
	}
	// voltages
	if len(rec.VoltagesGreen) != 16 || rec.VoltagesGreen[0] != 1 {
		t.Errorf("expected first green voltage=1, got %v", rec.VoltagesGreen)
	}
	if len(rec.VoltagesYellow) != 16 || rec.VoltagesYellow[0] != 2 {
		t.Errorf("expected first yellow voltage=2, got %v", rec.VoltagesYellow)
	}
	if len(rec.VoltagesRed) != 16 || rec.VoltagesRed[0] != 3 {
		t.Errorf("expected first red voltage=3, got %v", rec.VoltagesRed)
	}
	// monitor time
	exp := time.Date(2021, 7, 8, 9, 16, 18, 0, time.UTC)
	if !rec.MonitorTime.Equal(exp) {
		t.Errorf("expected MonitorTime=%v, got %v", exp, rec.MonitorTime)
	}
}

func TestRMSStatus_CVMFaultAndDefault(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	// MaxChannels small to speed up
	helperHdr := &helper.HeaderRecord{MaxChannels: 1, Volt220: true}

	// CVM Fault (fault=1)
	msg := baseMsgRMSStatus()
	msg[7] = 0x01
	msg[len(msg)-1] = computeChecksum(msg)
	rec, err := d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error for CVM fault: %v", err)
	}
	if !rec.IsFaulted || rec.Fault != "CVM Fault" {
		t.Errorf("expected CVM Fault, got IsFaulted=%v, Fault='%s'", rec.IsFaulted, rec.Fault)
	}

	// undefined fault (default)
	msg = baseMsgRMSStatus()
	msg[7] = 0xFF
	msg[len(msg)-1] = computeChecksum(msg)
	rec, err = d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error for default fault: %v", err)
	}
	if !rec.IsFaulted || rec.Fault != "Undefined Fault Type Error" {
		t.Errorf("expected default fault, got IsFaulted=%v, Fault='%s'", rec.IsFaulted, rec.Fault)
	}

	// check other green yellow red statuses
	msg = baseMsgRMSStatus()
	msg[26] = 0 // low byte
	msg[27] = 0 // high byte
	msg[28], msg[29] = 0x03, 0x00
	msg[30], msg[31] = 0x03, 0x00
	msg[len(msg)-1] = computeChecksum(msg)
	rec, err = d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error for default fault: %v", err)
	}
	// channel statuses
	if rec.ChannelGreenStatus[0] {
		t.Error("expected both green channels false")
	}
	if !rec.ChannelYellowStatus[0] {
		t.Error("expected both yellow channels true")
	}
	if !rec.ChannelRedStatus[0] {
		t.Error("expected both red channels true")
	}
}

func TestGetFault(t *testing.T) {
	tests := []struct {
		name        string
		fault       byte
		faultStatus uint32
		commVer     int
		wantValue   string
		wantStatus  string
	}{
		// simple faults 1–7
		{"CVM", 1, 0, 0, "CVM Fault", ""},
		{"24V-2", 2, 0, 0, "24V-2 Fault", ""},
		{"CVM&24V-2", 3, 0, 0, "CVM & 24V-2 Fault", ""},
		{"24V-1", 4, 0, 0, "24V-1 Fault", ""},
		{"CVM&24V-1", 5, 0, 0, "CVM & 24V-1 Fault", ""},
		{"24V1&24V2", 6, 0, 0, "24V-1 & 24V-2 Fault", ""},
		{"CVM&24V1&24V2", 7, 0, 0, "CVM & 24V-1 & 24V-2 Fault", ""},

		// watchdog/reserved/etc. 1*8 .. 9*8
		{"ExternalWD", 1 * 8, 0, 0, "External Watchdog Fault", ""},
		{"Reserved", 2 * 8, 0, 0, "reserved", ""},
		{"ProgramCardAjar", 3 * 8, 0, 0, "Program Card Ajar", ""},
		{"Conflict", 4 * 8, 0, 0, "Conflict Fault", ""},
		{"RedFail", 5 * 8, 0, 0, "Red Fail Fault", ""},
		{"ShortYellow", 6 * 8, 0, 0, "Clearance (Short Yellow) Fault", ""},
		{"SkippedYellow", 7 * 8, 0, 0, "Clearance (Skipped Yellow) Fault", ""},
		{"RYClearance", 8 * 8, 0, 0, "Red + Yellow Clearance Fault", ""},
		{"Port1Fail", 9 * 8, 0, 0, "Port 1 Fail", ""},

		// Diagnostic Fault 10*8 with all status subcases
		{"Diag200", 10 * 8, 200, 0, "Diagnostic Fault", "(Program card serial path error)"},
		{"Diag50", 10 * 8, 50, 0, "Diagnostic Fault", "(EEprom error)"},
		{"Diag40", 10 * 8, 40, 0, "Diagnostic Fault", "(RMS-Engine comm error)"},
		{"Diag33", 10 * 8, 33, 0, "Diagnostic Fault", "(RMS-Engine data error)"},
		{"Diag29", 10 * 8, 29, 0, "Diagnostic Fault", "(Switch serial path)"},
		{"Diag25", 10 * 8, 25, 0, "Diagnostic Fault", "(24V logic serial path)"},
		{"Diag22", 10 * 8, 22, 0, "Diagnostic Fault", "(trap error)"},
		{"Diag1", 10 * 8, 1, 0, "Diagnostic Fault", "(program card memory not found)"},
		{"Diag2", 10 * 8, 2, 0, "Diagnostic Fault", "(program card memory data error)"},
		{"Diag3", 10 * 8, 3, 0, "Diagnostic Fault", "(program card memory match error)"},
		{"DiagDef", 10 * 8, 999, 0, "Diagnostic Fault", ""},

		// 11*8 … 14*8
		{"DualInd", 11 * 8, 0, 0, "Dual Indication Fault", ""},
		{"FieldChk", 12 * 8, 0, 0, "Field Check Fault", ""},
		{"TypeChg", 13 * 8, 0, 0, "Type Change Fault", ""},
		{"LocalFlash", 14 * 8, 0, 0, "Local Flash Active", ""},

		// Configuration Change 15*8
		{"ConfigLowVer", 15 * 8, 1234, 0x32, "Configuration Change Fault", ""},                // commVer ≤ 0x32
		{"ConfigHighVer", 15 * 8, 42, 0x33, "Configuration Change Fault", "Check Value = 42"}, // commVer > 0x32

		// 16*8 … 21*8
		{"ACLow", 16 * 8, 0, 0, "AC Line Low Voltage", ""},
		{"RPConf", 17 * 8, 0, 0, "Recurrent Pulse Conflict Fault", ""},
		{"RPRed", 18 * 8, 0, 0, "Recurrent Pulse Red Fail Fault", ""},
		{"RPDual", 19 * 8, 0, 0, "Recurrent Pulse Dual Indication Fault", ""},
		{"48Vdc", 20 * 8, 0, 0, "48 Vdc Power Supply Fault", ""},
		{"FYAFlash", 21 * 8, 0, 0, "FYA Flash Rate Fault", ""},

		// FYA Yellow Trap for 22*8 and 23*8
		{"FYA22", 22 * 8, 0, 0, "Conflict Fault", "FYA Yellow Trap"},
		{"FYA23", 23 * 8, 0, 0, "Recurrent Pulse Conflict Fault", "FYA Yellow Trap"},

		// default
		{"Undefined", 0xFF, 0, 0, "Undefined Fault Type Error", ""},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			gotVal, gotStatus := getFault(tc.fault, tc.faultStatus, tc.commVer)
			if gotVal != tc.wantValue || gotStatus != tc.wantStatus {
				t.Errorf("getFault(%#x,%d,%#x) = (%q,%q), want (%q,%q)",
					tc.fault, tc.faultStatus, tc.commVer,
					gotVal, gotStatus, tc.wantValue, tc.wantStatus)
			}
		})
	}
}

func TestGetMMUFaultText(t *testing.T) {
	t.Parallel()
	tests := []struct {
		fault       int
		faultStatus int
		commVersion int
		wantString  string
		wantShow    bool
	}{
		// simple faults with no status
		{1, 0, 0, "CVM Fault", false},
		{2, 0, 0, "24V-2 Fault", false},
		{3, 0, 0, "CVM & 24V-2 Fault", false},
		{4, 0, 0, "24V-1 Fault", false},
		{5, 0, 0, "CVM & 24V-1 Fault", false},
		{6, 0, 0, "24V-1 & 24V-2 Fault", false},
		{7, 0, 0, "CVM & 24V-1 & 24V-2 Fault", false},
		{8, 0, 0, "External Watchdog Fault", false},
		{24, 0, 0, "Program Card Ajar Fault", false},

		// faults with implicit showFaultStatus=true
		{32, 0, 0, "Conflict Fault", true},
		{40, 0, 0, "Red Fail Fault", true},
		{48, 0, 0, "Clearance (Short Yellow) Fault", true},
		{56, 0, 0, "Clearance (Skipped Yellow) Fault", true},
		{64, 0, 0, "Clearance (Yellow + Red) Fault", true},

		// showFaultStatus=false defaults
		{72, 0, 0, "Port 1 Fault", false},

		// diagnostic nested statuses
		{80, 33, 0, "Diagnostic Fault (RMS-Engine A/D error)", false},
		{80, 40, 0, "Diagnostic Fault (RMS-Engine comm error)", false},
		{80, 50, 0, "Diagnostic Fault (EEprom error)", false},
		{80, 200, 0, "Diagnostic Fault (Program Card serial path)", false},
		{80, 29, 0, "Diagnostic Fault (Switch serial path)", false},
		{80, 25, 0, "Diagnostic Fault (24V logic serial path)", false},
		{80, 1, 0, "Diagnostic Fault (Program Card memory not found)", false},
		{80, 2, 0, "Diagnostic Fault (Program Card memory data error)", false},
		{80, 3, 0, "Diagnostic Fault (Program Card memory match error)", false},

		// dual, field, type, local flash
		{88, 0, 0, "Dual Indication Fault", true},
		{96, 0, 0, "Field Check Fault", true},
		{104, 0, 0, "Type Fault", false},
		{112, 0, 0, "Local Flash Active", false},

		// config change with version threshold
		{120, 10, 0x32, "Configuration Change Fault", false},
		{120, 42, 0x33, "Configuration Change Fault (42)", false},

		// beyond basic
		{136, 0, 0, "Recurrent Pulse Conflict Fault", true},
		{144, 0, 0, "Recurrent Pulse Red Fail Fault", true},
		{152, 0, 0, "Recurrent Pulse Dual Indication Fault", true},
		{160, 0, 0, "48 Vdc Power Supply Fault", true},
		{168, 0, 0, "FYA Flash Rate Fault", true},
		{176, 0, 0, "Conflict Fault, FYA Yellow Trap", true},
		{184, 0, 0, "Recurrent Pulse Conflict Fault, FYA Yellow Trap", true},

		// default
		{999, 0, 0, "undefined fault type error", true},
	}

	header := &helper.HeaderRecord{CommVersion: "0.0"}
	for _, tc := range tests {
		header.CommVersion = helper.ConvertByteToDecimalFormat(byte(tc.commVersion))
		gotStr, gotShow := GetMMUFaultText(tc.fault, tc.faultStatus, header)
		if gotStr != tc.wantString || gotShow != tc.wantShow {
			t.Errorf("GetMMUFaultText(%d,%d,commVer=%#x) = (%q,%v), want (%q,%v)",
				tc.fault, tc.faultStatus, tc.commVersion,
				gotStr, gotShow, tc.wantString, tc.wantShow)
		}
	}
}

func TestNormalizeVoltages(t *testing.T) {
	type args struct {
		input  int
		header *helper.HeaderRecord
	}
	tests := []struct {
		name string
		args args
		want int32
	}{
		{"no flags", args{10, &helper.HeaderRecord{Volt220: false, VoltDC: false}}, 10},
		{"volt220 only", args{10, &helper.HeaderRecord{Volt220: true, VoltDC: false}}, 20},
		{"voltDC only", args{20, &helper.HeaderRecord{Volt220: false, VoltDC: true}}, 5},
		{"both flags", args{8, &helper.HeaderRecord{Volt220: true, VoltDC: true}}, 4},
	}
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			if got := normalizeVoltages(tc.args.input, tc.args.header); got != tc.want {
				t.Errorf("normalizeVoltages(%d, %+v) = %d, want %d", tc.args.input, tc.args.header, got, tc.want)
			}
		})
	}
}

func makeFailRecords(ts0, ts1 int64) *helper.FaultSignalSequenceRecords {
	// helper to create two-record sequence
	return &helper.FaultSignalSequenceRecords{
		Records: []helper.TraceBuffer{{Timestamp: ts0}, {Timestamp: ts1}},
	}
}

func TestTraceRecordParser_InvalidLength(t *testing.T) {
	t.Parallel()
	// too short -> should return nil
	if tb := traceRecordParser([]byte{0, 1, 2}, &helper.HeaderRecord{MaxChannels: 1}); tb != nil {
		t.Errorf("expected nil for invalid length, got %+v", tb)
	}
}

func TestTraceRecordParser_AllFalseAndEE_SF_RE_False(t *testing.T) {
	t.Parallel()
	// 12-byte slice all zero -> everything false, AcVoltage=0, EE_SF_RE=false
	data := make([]byte, 12)
	hdr := &helper.HeaderRecord{MaxChannels: 4}
	tb := traceRecordParser(data, hdr)
	if tb == nil {
		t.Fatal("expected non-nil TraceBuffer")
	}
	// Timestamp = 0
	if tb.Timestamp != 0 {
		t.Errorf("Timestamp = %d; want 0", tb.Timestamp)
	}
	// All channel slices should be len=4, all false
	for i, sl := range [][]bool{tb.Greens, tb.Yellows, tb.Reds, tb.Walks} {
		if len(sl) != 4 {
			t.Errorf("slice %d length = %d; want 4", i, len(sl))
			continue
		}
		for j, v := range sl {
			if v {
				t.Errorf("slice %d[%d] = true; want false", i, j)
			}
		}
	}
	if tb.EE_SF_RE {
		t.Error("EE_SF_RE = true; want false")
	}
	if tb.AcVoltage != 0 {
		t.Errorf("AcVoltage = %d; want 0", tb.AcVoltage)
	}
}

func TestTraceRecordParser_EE_SF_RE_True(t *testing.T) {
	t.Parallel()
	// build a minimal 12-byte packet
	data := make([]byte, 12)
	// set bit7 in byte[10]
	data[10] = 0x80
	// everything else can be zero
	hdr := &helper.HeaderRecord{MaxChannels: 1}

	tb := traceRecordParser(data, hdr)
	if tb == nil {
		t.Fatal("TraceBuffer should not be nil")
	}

	if !tb.EE_SF_RE {
		t.Errorf("EE_SF_RE = false; want true when bit7 of byte 10 is set")
	}
}

// Add missing test cases for LogPreviousFail edge cases
func TestLogPreviousFail_AdditionalCases(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	t.Run("Type16Mode with conflicting channels", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{Volt220: false}
		msg := buildMsgPrevFail(1, func(rec []byte) {
			// Set fault = 64 (clearance fault) to trigger NextConflictingChannels logic
			rec[0] = 64
			// Set controlStatus byte to enable type16Mode (bit 1)
			rec[27] = 0x02
			// Set valid BCD time
			rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x02, 0x14
			// Set ggfonStatus to test NextConflictingChannels
			rec[17] = 0x01 // low byte
			rec[18] = 0x00 // high byte
		})

		result, err := dev.LogPreviousFail(httpHdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if len(result.Records) != 1 {
			t.Fatalf("expected 1 record, got %d", len(result.Records))
		}

		record := result.Records[0]
		// In type16Mode, we expect 16 channels (j=15)
		if len(record.ChannelRedStatus) != 16 {
			t.Errorf("expected 16 red channels in type16Mode, got %d", len(record.ChannelRedStatus))
		}

		// Check NextConflictingChannels is populated for fault 64
		if len(record.NextConflictingChannels) == 0 {
			t.Error("expected NextConflictingChannels to be populated for fault 64")
		}
	})

	t.Run("SDLC Type-12 mode walk field check mapping", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{Volt220: false}
		msg := buildMsgPrevFail(1, func(rec []byte) {
			// Set controlStatus to enable SDLC TYPE-12 mode (bit 2)
			rec[27] = 0x04
			// Set valid BCD time
			rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x02, 0x14
			// Set fcStatusG with high bits to test walk FC mapping
			rec[3] = 0x00 // low byte
			rec[4] = 0x0F // high byte (bits 8,9,10,11 set)
		})

		result, err := dev.LogPreviousFail(httpHdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		record := result.Records[0]
		// Check that walk field check status is populated when fcStatusG has high bits
		if len(record.ChannelWalkFieldCheckStatus) == 0 {
			t.Error("expected ChannelWalkFieldCheckStatus to be populated in SDLC TYPE-12 mode")
		}
	})

	t.Run("Volt220 voltage normalization", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{Volt220: true}
		msg := buildMsgPrevFail(1, func(rec []byte) {
			// Set valid BCD time
			rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x02, 0x14
			// Set some RMS voltages
			rec[55] = 50 // greenChannel1RmsVoltage
			rec[71] = 60 // yellowChannel1RmsVoltage
			rec[87] = 70 // redChannel1RmsVoltage
		})

		result, err := dev.LogPreviousFail(httpHdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		record := result.Records[0]
		// With Volt220=true, voltages should be doubled
		if len(record.ChannelGreenRmsVoltage) > 0 && record.ChannelGreenRmsVoltage[0] != 100 {
			t.Errorf("expected green voltage 100 (50*2), got %d", record.ChannelGreenRmsVoltage[0])
		}
	})
}

// Add missing test cases for RMSStatus additional error conditions
func TestRMSStatus_AdditionalErrors(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2}

	testCases := []struct {
		name    string
		modify  func([]byte)
		wantErr error
	}{
		{
			name: "InvalidDayBCD",
			modify: func(msg []byte) {
				msg[42] = 0x32 // day = 50 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name: "InvalidHourBCD",
			modify: func(msg []byte) {
				msg[41] = 0x25 // hour = 37 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name: "InvalidMinBCD",
			modify: func(msg []byte) {
				msg[40] = 0x61 // min = 97 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name: "InvalidSecBCD",
			modify: func(msg []byte) {
				msg[39] = 0x61 // sec = 97 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := baseMsgRMSStatus()
			tc.modify(msg)
			_, err := d.RMSStatus(hdr, msg, helperHdr)
			if !errors.Is(err, tc.wantErr) {
				t.Errorf("expected error %v, got %v", tc.wantErr, err)
			}
		})
	}
}

// Add test for specific RMSStatus fault cases that might be missing
func TestRMSStatus_AdditionalFaultCases(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2, Volt220: true}

	t.Run("Volt220 voltage multiplication", func(t *testing.T) {
		t.Parallel()
		msg := baseMsgRMSStatus()
		msg[7] = 0x00 // no fault
		// Set specific voltage values to test Volt220 multiplication
		// Note: voltage arrays are reversed in the code (62 down to 47 for green)
		msg[62] = 25 // first green voltage (channel 0)
		msg[78] = 30 // first yellow voltage (channel 0)
		msg[94] = 35 // first red voltage (channel 0)
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := d.RMSStatus(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// With Volt220=true, voltages should be doubled
		if rec.VoltagesGreen[0] != 50 {
			t.Errorf("expected green voltage 50 (25*2), got %d", rec.VoltagesGreen[0])
		}
		if rec.VoltagesYellow[0] != 60 {
			t.Errorf("expected yellow voltage 60 (30*2), got %d", rec.VoltagesYellow[0])
		}
		if rec.VoltagesRed[0] != 70 {
			t.Errorf("expected red voltage 70 (35*2), got %d", rec.VoltagesRed[0])
		}
	})

	t.Run("Specific fault types with faultStatus", func(t *testing.T) {
		t.Parallel()
		msg := baseMsgRMSStatus()
		msg[7] = 32   // Conflict Fault
		msg[8] = 0x01 // faultStatus low byte
		msg[9] = 0x00 // faultStatus high byte
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := d.RMSStatus(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if !rec.IsFaulted {
			t.Error("expected IsFaulted=true for fault=32")
		}
		if rec.Fault != "Conflict Fault" {
			t.Errorf("expected Fault='Conflict Fault', got '%s'", rec.Fault)
		}
	})
}

// Add test for LogConfiguration edge cases
func TestLogConfiguration_AdditionalEdgeCases(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	t.Run("Invalid byte length", func(t *testing.T) {
		t.Parallel()
		hh := &helper.HeaderRecord{MaxChannels: 16}
		msg := make([]byte, 10) // too short
		msg[7] = 1              // 1 record

		_, err := dev.LogConfiguration(hdr, msg, hh)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("expected byte length error, got %v", err)
		}
	})

	t.Run("Checksum validation error", func(t *testing.T) {
		t.Parallel()
		hh := &helper.HeaderRecord{MaxChannels: 16}
		msg := logConfigurationBuildValidMessage(1)
		msg[len(msg)-1] = 0xFF // corrupt checksum

		_, err := dev.LogConfiguration(hdr, msg, hh)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("expected checksum error, got %v", err)
		}
	})

	t.Run("Legacy FYA with different Type mode", func(t *testing.T) {
		t.Parallel()
		hh := &helper.HeaderRecord{
			MaxChannels:      16,
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74),
			CommVersion:      helper.ConvertByteToDecimalFormat(0x30), // < 0x38 for legacy
		}

		msg := buildBaseConfigMsg(1, func(rec []byte) {
			// Set valid BCD time
			rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x00, 0x02, 0x01, 0x21
			// Set selectJumpers1 for Type 12 mode and FYA bits
			rec[53] = 0x78 // bits 3,4,5,6 set for FYA channels, bit 0 clear for Type 12
		})

		result, err := dev.LogConfiguration(hdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		record := result.Record[0]
		// Should use legacy FYA logic for Type 12 mode
		if record.TypeMode != "12" {
			t.Errorf("expected TypeMode=12, got %s", record.TypeMode)
		}
	})
}

// Add test for LogACLineEvent additional cases
func TestLogACLineEvent_AdditionalCases(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	t.Run("MainsDC voltage type", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{MainsDC: true}
		recs := [][]byte{
			{1, 25, 0x45, 0x16, 0x05, 0x28, 0x02, 0x14}, // eventType=1, voltage=25, valid BCD date
		}
		msg := buildMsgLogACLineEvent(1, recs)

		result, err := dev.LogACLineEvent(hdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// MainsDC should set VoltageType to 2
		if result.VoltageType != 2 {
			t.Errorf("expected VoltageType=2 for MainsDC, got %d", result.VoltageType)
		}
	})

	t.Run("Voltage below blackout level", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{MainsDC: false, BlackoutLevel: 30}
		recs := [][]byte{
			{1, 20, 0x45, 0x16, 0x05, 0x28, 0x02, 0x14}, // voltage=20 < blackoutLevel=30, valid BCD date
		}
		msg := buildMsgLogACLineEvent(1, recs)

		result, err := dev.LogACLineEvent(hdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Voltage below blackout level should be set to 0
		if result.Records[0].LineVoltageRms != 0 {
			t.Errorf("expected LineVoltageRms=0 for voltage below blackout, got %d", result.Records[0].LineVoltageRms)
		}
	})
}

// Add test for MonitorIDandName edge case
func TestMonitorIDandName_EdgeCase(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}
	helperHdr := &helper.HeaderRecord{}

	t.Run("Monitor name with null bytes", func(t *testing.T) {
		t.Parallel()
		msg := make([]byte, 38)
		// Set monitor ID
		msg[5] = 0xFF // LS
		msg[6] = 0xFF // MS -> should give 0xFFFF = 65535
		// Set name with embedded null bytes
		copy(msg[7:], []byte("Test\x00Name\x00"))
		msg[len(msg)-1] = computeChecksum(msg)

		mon, err := dev.MonitorIDandName(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if mon.MonitorId != 65535 {
			t.Errorf("MonitorId = %d; want 65535", mon.MonitorId)
		}

		// GetMonitorName should handle null bytes properly
		if mon.MonitorName == "" {
			t.Error("MonitorName should not be empty")
		}
	})
}

// Add test for TraceRecordParser additional edge cases
func TestTraceRecordParser_AdditionalCases(t *testing.T) {
	t.Parallel()

	t.Run("Complex timestamp and channel status", func(t *testing.T) {
		t.Parallel()
		data := make([]byte, 12)
		// Set complex timestamp (bytes 0-1): 0x1234 = 4660
		data[0] = 0x12
		data[1] = 0x34

		// Set some channel statuses
		data[2] = 0x03 // green low: channels 0,1 on
		data[3] = 0x00 // green high
		data[4] = 0x0C // yellow low: channels 2,3 on
		data[5] = 0x00 // yellow high
		data[6] = 0x30 // red low: channels 4,5 on
		data[7] = 0x00 // red high
		data[8] = 0xC0 // walk low: channels 6,7 on
		data[9] = 0x00 // walk high

		data[10] = 0x00 // EE_SF_RE = false
		data[11] = 42   // AcVoltage = 42

		hdr := &helper.HeaderRecord{MaxChannels: 8}
		tb := traceRecordParser(data, hdr)

		if tb == nil {
			t.Fatal("expected non-nil TraceBuffer")
		}

		if tb.Timestamp != 4660 {
			t.Errorf("Timestamp = %d; want 4660", tb.Timestamp)
		}

		// Check specific channel statuses
		if !tb.Greens[0] || !tb.Greens[1] {
			t.Error("expected green channels 0,1 to be true")
		}
		if !tb.Yellows[2] || !tb.Yellows[3] {
			t.Error("expected yellow channels 2,3 to be true")
		}
		if !tb.Reds[4] || !tb.Reds[5] {
			t.Error("expected red channels 4,5 to be true")
		}
		if !tb.Walks[6] || !tb.Walks[7] {
			t.Error("expected walk channels 6,7 to be true")
		}

		if tb.AcVoltage != 42 {
			t.Errorf("AcVoltage = %d; want 42", tb.AcVoltage)
		}
	})
}

func TestLogConfiguration_MissingBranchCoverage(t *testing.T) {
	dev := EDIMMU216LE{}
	baseHdr := logConfigurationBaseHeader()

	t.Run("FYA_Remap_ModeI_With_Pairs", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74),
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Mode I remap: fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50, with channel pairs
		msg[7+1+51] = 0x51 // fyaEnable1: Mode I with bit 0 set
		msg[7+1+52] = 0x40 // fyaEnable2: fya_remap bit set
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		if !strings.Contains(fya, "Mode I") || !strings.Contains(fya, "1-9") {
			t.Errorf("Expected Mode I with 1-9 pairs, got %q", fya)
		}
	})

	t.Run("FYA_ErrorMode_InvalidCombination", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToString(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Invalid combination that triggers default case - 0xC0 doesn't match any valid case
		msg[7+1+51] = 0xC0 // Invalid combination - high nibble 0xC0 doesn't match any case
		msg[7+1+52] = 0x00 // No remap
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		if !strings.Contains(fya, "Mode <error>") {
			t.Errorf("Expected Mode <error> for invalid combination, got %q", fya)
		}
	})

	t.Run("FyaRedAndGreenDisable_Mode6_HighFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Mode G (6): fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50
		msg[7+1+51] = 0x50 // Mode G
		msg[7+1+52] = 0x02 // fyaEnable2 with bit 1 set
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// For Mode G with high firmware revision, should set FyaRedAndGreenDisable
		if rec.Record[0].FyaRedAndGreenDisable != "Channels: 10" {
			t.Errorf("Expected FyaRedAndGreenDisable for mode 6, got %q", rec.Record[0].FyaRedAndGreenDisable)
		}
	})

	t.Run("LegacyFYA_Type16_WithFYAC", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x37), // < 0x38 for legacy
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// selectJumpers1: Type 16 (bit 0) + FYA bits + FYAC (bit 7)
		msg[7+1+53] = 0xF9 // 0x78 (FYA bits) + 0x80 (FYAC) + 0x01 (Type 16)
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		// Legacy FYA logic overwrites string, so only last condition (7-16) is kept
		if !strings.Contains(fya, "7-16") || !strings.Contains(fya, "(Mode=FYAC)") {
			t.Errorf("Expected 7-16 (Mode=FYAC) for legacy FYA, got %q", fya)
		}
	})

	t.Run("Pplt5Suppression_Type16_HighFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29), // > 0x28
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// options2 with high nibble set, selectJumpers1 with suppression bits + Type 16
		msg[7+1+50] = 0xF0 // options2 high nibble set
		msg[7+1+53] = 0xF1 // suppression bits + Type 16
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// For Type 16 with high firmware, should use 1-9, 3-10, 5-11, 7-12
		pplt5 := rec.Record[0].Pplt5Suppression
		if !strings.Contains(pplt5, "1-9") || !strings.Contains(pplt5, "3-10") || !strings.Contains(pplt5, "5-11") || !strings.Contains(pplt5, "7-12") {
			t.Errorf("Expected Type 16 high FW channels in PPLT5, got %q", pplt5)
		}
	})

	t.Run("FYA_ChannelPairs_Case1357911", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode B (0x20) which gives nema_FyaMode = 1, and set low nibble to 0x0F to trigger all channel pairs
		msg[7+1+51] = 0x2F // fyaEnable1: Mode B (0x20) + all channel pairs (0x0F)
		msg[7+1+52] = 0x00 // fyaEnable2: no remap
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		// Should hit case 1 and show 13-16 channel pairs
		if !strings.Contains(fya, "1-13") || !strings.Contains(fya, "3-14") || !strings.Contains(fya, "3-15") || !strings.Contains(fya, "7-16") {
			t.Errorf("Expected 13-16 channel pairs for nema_FyaMode=1, got %q", fya)
		}
	})

	t.Run("FYA_RedYellowEnable_Case01", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode A (0x00) which gives nema_FyaMode = 0
		msg[7+1+51] = 0x00 // fyaEnable1: Mode A (high nibble = 0)
		msg[7+1+52] = 0x0F // fyaEnable2: set low nibble to trigger all case 0,1 branches
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Should hit case 0 in FyaRedAndYellowEnable logic
		if rec.Record[0].FyaRedAndYellowEnable != "Channels: 7" {
			t.Errorf("Expected FyaRedAndYellowEnable='Channels: 7', got %q", rec.Record[0].FyaRedAndYellowEnable)
		}
	})

	t.Run("FYA_IndividualChannelPairs_Case1357911", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test individual channel pairs for case 1,3,5,7,9,11
		testCases := []struct {
			name           string
			fyaEnable1     byte
			expectedString string
		}{
			{"bit2_3-14", 0x22, "3-14"}, // Mode B (0x20) + bit 1 (0x02) -> 3-14
			{"bit4_3-15", 0x24, "3-15"}, // Mode B (0x20) + bit 2 (0x04) -> 3-15
			{"bit8_7-16", 0x28, "7-16"}, // Mode B (0x20) + bit 3 (0x08) -> 7-16
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaEnable1 // fyaEnable1
				msg[7+1+52] = 0x00          // fyaEnable2: no remap
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				fya := rec.Record[0].FlashingYellowArrows[0]
				if !strings.Contains(fya, tc.expectedString) {
					t.Errorf("Expected %s in FYA, got %q", tc.expectedString, fya)
				}
			})
		}
	})

	t.Run("FYA_RedYellowEnable_Case24810", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode C (0x90) which gives nema_FyaMode = 2
		msg[7+1+51] = 0x90 // fyaEnable1: Mode C (fya_fYa_lo + fya_ry_lo = 0x80+0x10)
		msg[7+1+52] = 0x01 // fyaEnable2: set bit 0 to trigger case 2,4,8,10
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Should hit case 2 in FyaRedAndYellowEnable logic
		if rec.Record[0].FyaRedAndYellowEnable != "Channels: 9" {
			t.Errorf("Expected FyaRedAndYellowEnable='Channels: 9', got %q", rec.Record[0].FyaRedAndYellowEnable)
		}
	})

	t.Run("FYA_RedYellowEnable_Case35911", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode D (0xB0) which gives nema_FyaMode = 3
		msg[7+1+51] = 0xB0 // fyaEnable1: Mode D (fya_msb_hi + fya_fYa_lo + fya_ry_lo = 0x20+0x80+0x10)
		msg[7+1+52] = 0x01 // fyaEnable2: set bit 0 to trigger case 3,5,9,11
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Should hit case 3 in FyaRedAndYellowEnable logic
		if rec.Record[0].FyaRedAndYellowEnable != "Channels: 13" {
			t.Errorf("Expected FyaRedAndYellowEnable='Channels: 13', got %q", rec.Record[0].FyaRedAndYellowEnable)
		}
	})

	t.Run("FYA_AdditionalIndividualBits", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test additional individual bits that are still uncovered
		testCases := []struct {
			name       string
			fyaEnable2 byte
			nemaMode   byte // Mode to set nema_FyaMode
			expectedCh string
		}{
			{"case24810_bit2", 0x02, 0x90, "10"}, // Mode C (nema_FyaMode=2) + fyaEnable2 bit 1
			{"case24810_bit4", 0x04, 0x90, "11"}, // Mode C (nema_FyaMode=2) + fyaEnable2 bit 2
			{"case24810_bit8", 0x08, 0x90, "12"}, // Mode C (nema_FyaMode=2) + fyaEnable2 bit 3
			{"case35911_bit2", 0x02, 0xB0, "14"}, // Mode D (nema_FyaMode=3) + fyaEnable2 bit 1
			{"case35911_bit4", 0x04, 0xB0, "15"}, // Mode D (nema_FyaMode=3) + fyaEnable2 bit 2
			{"case35911_bit8", 0x08, 0xB0, "16"}, // Mode D (nema_FyaMode=3) + fyaEnable2 bit 3
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.nemaMode   // fyaEnable1 to set nema_FyaMode
				msg[7+1+52] = tc.fyaEnable2 // fyaEnable2 to trigger individual bits
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				expected := "Channels: " + tc.expectedCh
				if rec.Record[0].FyaRedAndYellowEnable != expected {
					t.Errorf("Expected FyaRedAndYellowEnable='%s', got %q", expected, rec.Record[0].FyaRedAndYellowEnable)
				}
			})
		}
	})

	t.Run("FYA_Case6_FyaRedAndGreenDisable", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 6 (Mode G) FyaRedAndGreenDisable individual bits
		testCases := []struct {
			name       string
			fyaEnable2 byte
			expectedCh string
		}{
			{"case6_bit1", 0x01, "9"},  // fyaEnable2 bit 0 -> Channel 9
			{"case6_bit4", 0x04, "11"}, // fyaEnable2 bit 2 -> Channel 11
			{"case6_bit8", 0x08, "12"}, // fyaEnable2 bit 3 -> Channel 12
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = 0x50          // Mode G (fya_srcY + fya_ry_lo = 0x40+0x10)
				msg[7+1+52] = tc.fyaEnable2 // fyaEnable2 to trigger individual bits
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				expected := "Channels: " + tc.expectedCh
				if rec.Record[0].FyaRedAndGreenDisable != expected {
					t.Errorf("Expected FyaRedAndGreenDisable='%s', got %q", expected, rec.Record[0].FyaRedAndGreenDisable)
				}
			})
		}
	})

	t.Run("FYA_Case7_FyaRedAndGreenDisable", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 7 (Mode H) FyaRedAndGreenDisable individual bits
		testCases := []struct {
			name       string
			fyaEnable2 byte
			expectedCh string
		}{
			{"case7_bit1", 0x01, "13"}, // fyaEnable2 bit 0 -> Channel 13
			{"case7_bit2", 0x02, "14"}, // fyaEnable2 bit 1 -> Channel 14
			{"case7_bit4", 0x04, "15"}, // fyaEnable2 bit 2 -> Channel 15
			{"case7_bit8", 0x08, "16"}, // fyaEnable2 bit 3 -> Channel 16
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = 0x70          // Mode H (fya_msb_hi + fya_srcY + fya_ry_lo = 0x20+0x40+0x10)
				msg[7+1+52] = tc.fyaEnable2 // fyaEnable2 to trigger individual bits
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				expected := "Channels: " + tc.expectedCh
				if rec.Record[0].FyaRedAndGreenDisable != expected {
					t.Errorf("Expected FyaRedAndGreenDisable='%s', got %q", expected, rec.Record[0].FyaRedAndGreenDisable)
				}
			})
		}
	})

	t.Run("FYA_Case67_LowFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x73), // <= 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 6,7 with low firmware revision (else branch)
		testCases := []struct {
			name     string
			fyaMode  byte
			modeName string
		}{
			{"case6_low_fw", 0x50, "G"}, // Mode G with low firmware
			{"case7_low_fw", 0x70, "H"}, // Mode H with low firmware
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaMode // fyaEnable1 to set mode
				msg[7+1+52] = 0x01       // fyaEnable2 with some bits set
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				// Should hit else branch and set FyaRedAndYellowEnable
				if rec.Record[0].FyaRedAndYellowEnable != "Channels: <none>" {
					t.Errorf("Expected FyaRedAndYellowEnable='Channels: <none>' for low firmware, got %q", rec.Record[0].FyaRedAndYellowEnable)
				}
			})
		}
	})

	t.Run("FYA_Case67_NoFyaEnable2", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 6,7 with fyaEnable2&0xf == 0 (else branch)
		testCases := []struct {
			name     string
			fyaMode  byte
			modeName string
		}{
			{"case6_no_enable2", 0x50, "G"}, // Mode G with no fyaEnable2
			{"case7_no_enable2", 0x70, "H"}, // Mode H with no fyaEnable2
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaMode // fyaEnable1 to set mode
				msg[7+1+52] = 0x00       // fyaEnable2 with no low nibble bits set
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				// Should hit else branch and set FyaRedAndGreenDisable to <none>
				if rec.Record[0].FyaRedAndGreenDisable != "Channels: <none>" {
					t.Errorf("Expected FyaRedAndGreenDisable='Channels: <none>' for no fyaEnable2, got %q", rec.Record[0].FyaRedAndGreenDisable)
				}
			})
		}
	})

	t.Run("PPLT5_Type16_LowFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x28), // <= 0x28
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// options2 with high nibble set, selectJumpers1 with Type 16 + suppression bits
		msg[7+1+50] = 0xF0 // options2 high nibble set
		msg[7+1+53] = 0xF1 // Type 16 (bit 0) + suppression bits (0x10,0x20,0x40,0x80)
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// For Type 16 with low firmware, should use 1-13, 3-14, 5-15, 7-16
		pplt5 := rec.Record[0].Pplt5Suppression
		if !strings.Contains(pplt5, "1-13") || !strings.Contains(pplt5, "3-14") || !strings.Contains(pplt5, "5-15") || !strings.Contains(pplt5, "7-16") {
			t.Errorf("Expected Type 16 low FW channels in PPLT5, got %q", pplt5)
		}
	})

	t.Run("PPLT5_Type12_IndividualBits", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test individual PPLT5 suppression bits for Type 12
		testCases := []struct {
			name           string
			selectJumpers1 byte
			expectedCh     string
		}{
			{"type12_bit10", 0x10, "1-9"},  // Type 12 + bit 4 -> 1-9
			{"type12_bit20", 0x20, "3-10"}, // Type 12 + bit 5 -> 3-10
			{"type12_bit40", 0x40, "5-11"}, // Type 12 + bit 6 -> 5-11
			{"type12_bit80", 0x80, "7-12"}, // Type 12 + bit 7 -> 7-12
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+50] = 0xF0              // options2 high nibble set
				msg[7+1+53] = tc.selectJumpers1 // Type 12 (no bit 0) + individual suppression bit
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				if !strings.Contains(rec.Record[0].Pplt5Suppression, tc.expectedCh) {
					t.Errorf("Expected %s in PPLT5 suppression, got %q", tc.expectedCh, rec.Record[0].Pplt5Suppression)
				}
			})
		}
	})

	t.Run("FYA_FinalMissingChannelPairs", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test the final missing individual channel pair conditions in case 1,3,5,7,9,11
		// These are the exact lines that are still uncovered (lines 1018, 1021, 1024)
		testCases := []struct {
			name           string
			fyaEnable1     byte
			expectedString string
		}{
			{"missing_bit2", 0x22, "3-14"}, // Mode B (0x20) + bit 1 (0x02) -> should hit fyaEnable1&0x2 > 0 body
			{"missing_bit4", 0x24, "3-15"}, // Mode B (0x20) + bit 2 (0x04) -> should hit fyaEnable1&0x4 > 0 body
			{"missing_bit8", 0x28, "7-16"}, // Mode B (0x20) + bit 3 (0x08) -> should hit fyaEnable1&0x8 > 0 body
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaEnable1 // fyaEnable1 to trigger nema_FyaMode=1 and specific bit
				msg[7+1+52] = 0x00          // fyaEnable2: no remap
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				fya := rec.Record[0].FlashingYellowArrows[0]
				if !strings.Contains(fya, tc.expectedString) {
					t.Errorf("Expected %s in FYA channel pairs, got %q", tc.expectedString, fya)
				}
			})
		}
	})

	t.Run("FYA_MissingCase024681012_ChannelPairs", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test the missing individual channel pair conditions in case 0,2,4,6,8,10
		// These are the exact lines that are still uncovered
		testCases := []struct {
			name           string
			fyaEnable1     byte
			expectedString string
		}{
			{"case0_bit2", 0x02, "3-10"}, // Mode A (0x00) + bit 1 (0x02) -> should hit fyaEnable1&0x2 > 0 body for case 0
			{"case0_bit4", 0x04, "5-11"}, // Mode A (0x00) + bit 2 (0x04) -> should hit fyaEnable1&0x4 > 0 body for case 0
			{"case0_bit8", 0x08, "7-12"}, // Mode A (0x00) + bit 3 (0x08) -> should hit fyaEnable1&0x8 > 0 body for case 0
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaEnable1 // fyaEnable1 to trigger nema_FyaMode=0 and specific bit
				msg[7+1+52] = 0x00          // fyaEnable2: no remap
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				fya := rec.Record[0].FlashingYellowArrows[0]
				if !strings.Contains(fya, tc.expectedString) {
					t.Errorf("Expected %s in FYA channel pairs, got %q", tc.expectedString, fya)
				}
			})
		}
	})
}
