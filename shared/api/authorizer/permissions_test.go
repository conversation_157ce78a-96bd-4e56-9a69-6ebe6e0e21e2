package authorizer

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// TestGetUserPermissionsStructure tests that the permission structures are properly defined
func TestGetUserPermissionsStructure(t *testing.T) {
	// Test that we can create the permission structures
	permissions := &UserPermissions{
		UserID: "550e8400-e29b-41d4-a716-************",
		Permissions: []Permission{
			{
				Scope:          "organization",
				ScopeID:        "550e8400-e29b-41d4-a716-************",
				OrganizationID: "550e8400-e29b-41d4-a716-************",
				Permissions:    []string{"org_view_users", "org_manage_users", "org_delete_users"},
			},
			{
				Scope:          "device_group",
				ScopeID:        "550e8400-e29b-41d4-a716-************",
				OrganizationID: "550e8400-e29b-41d4-a716-************",
				Permissions:    []string{"device_group_manage_devices", "device_group_delete_devices"},
			},
		},
	}

	// Verify the structure is correct
	assert.Equal(t, "550e8400-e29b-41d4-a716-************", permissions.UserID, "User ID should match expected value")
	assert.Len(t, permissions.Permissions, 2, "Should have exactly 2 permission groups")

	// Verify organization permissions
	orgPerm := permissions.Permissions[0]
	assert.Equal(t, "organization", orgPerm.Scope, "First permission should be organization scope")
	assert.Equal(t, "550e8400-e29b-41d4-a716-************", orgPerm.ScopeID, "Organization scope ID should match")
	assert.Len(t, orgPerm.Permissions, 3, "Organization should have exactly 3 permissions")

	// Verify device group permissions
	dgPerm := permissions.Permissions[1]
	assert.Equal(t, "device_group", dgPerm.Scope, "Second permission should be device_group scope")
	assert.Equal(t, "550e8400-e29b-41d4-a716-************", dgPerm.ScopeID, "Device group scope ID should match")
	assert.Len(t, dgPerm.Permissions, 2, "Device group should have exactly 2 permissions")
}

// TestGetUserPermissionsWithMockData tests the function with mock database responses
func TestGetUserPermissionsWithMockData(t *testing.T) {
	// Create mock database executor using the existing FakeDBExecutor
	mockDB := &dbexecutor.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Return empty results for all queries to test the function structure
			// The slice is already initialized as empty, so no need to do anything
			return nil
		},
	}

	// Test the function with valid UUID
	permissions, err := GetUserPermissions(mockDB, "550e8400-e29b-41d4-a716-************")
	assert.NoError(t, err, "Should not return error for valid UUID with mock data")
	assert.NotNil(t, permissions, "Permissions should not be nil")
	assert.NotNil(t, permissions.Permissions, "Permissions slice should be initialized")
	assert.Empty(t, permissions.Permissions, "Should have no permissions with empty mock data")
	assert.Equal(t, "550e8400-e29b-41d4-a716-************", permissions.UserID, "User ID should match input")
}

// TestGetUserPermissionsWithSampleData tests the function with sample permission data
func TestGetUserPermissionsWithSampleData(t *testing.T) {
	// Create mock database executor with sample data
	mockDB := &dbexecutor.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Cast dest to the expected slice type and populate with sample data
			if slice, ok := dest.(*[]Permission); ok {
				*slice = []Permission{
					{
						Scope:          "org", // Using actual scope from PermissionGroup table
						ScopeID:        "550e8400-e29b-41d4-a716-************",
						OrganizationID: "550e8400-e29b-41d4-a716-************",
						Permissions:    []string{"org_view_users", "org_manage_users"},
					},
					{
						Scope:          "device_group", // Using actual scope from PermissionGroup table
						ScopeID:        "550e8400-e29b-41d4-a716-************",
						OrganizationID: "550e8400-e29b-41d4-a716-************",
						Permissions:    []string{"device_group_manage_devices"},
					},
				}
			}
			return nil
		},
	}

	// Test the function with valid UUID
	permissions, err := GetUserPermissions(mockDB, "550e8400-e29b-41d4-a716-************")
	assert.NoError(t, err, "Should not return error for valid UUID with sample data")
	assert.NotNil(t, permissions, "Permissions should not be nil")
	assert.Len(t, permissions.Permissions, 2, "Should have exactly 2 permission groups")

	// Find and verify organization permissions
	var orgPerm *Permission
	var dgPerm *Permission

	for i := range permissions.Permissions {
		if permissions.Permissions[i].Scope == "org" {
			orgPerm = &permissions.Permissions[i]
		} else if permissions.Permissions[i].Scope == "device_group" {
			dgPerm = &permissions.Permissions[i]
		}
	}

	// Verify organization permissions
	assert.NotNil(t, orgPerm, "Should find organization permissions")
	if orgPerm != nil {
		assert.Equal(t, "550e8400-e29b-41d4-a716-************", orgPerm.ScopeID, "Organization scope ID should match")
		assert.Len(t, orgPerm.Permissions, 2, "Organization should have exactly 2 permissions")

		// Verify the actual permissions
		expectedPerms := []string{"org_view_users", "org_manage_users"}
		for _, expectedPerm := range expectedPerms {
			assert.Contains(t, orgPerm.Permissions, expectedPerm, "Organization permissions should contain %s", expectedPerm)
		}
	}

	// Verify device group permissions
	assert.NotNil(t, dgPerm, "Should find device group permissions")
	if dgPerm != nil {
		assert.Equal(t, "550e8400-e29b-41d4-a716-************", dgPerm.ScopeID, "Device group scope ID should match")
		assert.Len(t, dgPerm.Permissions, 1, "Device group should have exactly 1 permission")
		assert.Equal(t, "device_group_manage_devices", dgPerm.Permissions[0], "Device group should have manage_devices permission")
	}
}

// TestGetUserPermissionsInvalidUUID tests that invalid UUIDs are properly rejected
func TestGetUserPermissionsInvalidUUID(t *testing.T) {
	// Create mock database executor
	mockDB := &dbexecutor.FakeDBExecutor{}

	// Test with invalid UUID
	permissions, err := GetUserPermissions(mockDB, "invalid-uuid")
	assert.Error(t, err, "Should return error for invalid UUID")
	assert.Nil(t, permissions, "Permissions should be nil for invalid UUID")
	assert.Contains(t, err.Error(), "invalid user ID format", "Error should mention invalid UUID format")

	// Test with empty UUID (should be allowed)
	permissions, err = GetUserPermissions(mockDB, "")
	assert.NoError(t, err, "Should not return error for empty UUID")
	assert.NotNil(t, permissions, "Permissions should not be nil for empty UUID")
	assert.Equal(t, "", permissions.UserID, "User ID should be empty")
}

// TestUserPermissions_CanAccessDeviceByOrigID tests the CanAccessDeviceByOrigID method
func TestUserPermissions_CanAccessDeviceByOrigID(t *testing.T) {
	tests := []struct {
		name                string
		userPermissions     *UserPermissions
		origID              int64
		requiredPermissions []string
		mockDeviceInfo      *DeviceInfo
		mockError           error
		expectedDeviceID    string
		expectError         bool
	}{
		{
			name: "user_with_org_permission_can_access_device",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-************",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "org-1",
						OrganizationID: "org-1",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			origID:              123,
			requiredPermissions: []string{"org_view_devices"},
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-123",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{"group-1"},
				LocationGroupIDs: []string{"location-1"},
			},
			expectedDeviceID: "device-uuid-123",
			expectError:      false,
		},
		{
			name: "user_with_device_group_permission_can_access_device",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-************",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "org-1",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			origID:              456,
			requiredPermissions: []string{"device_group_view_devices"},
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-456",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{"group-1", "group-2"},
				LocationGroupIDs: []string{},
			},
			expectedDeviceID: "device-uuid-456",
			expectError:      false,
		},
		{
			name: "user_with_location_group_permission_can_access_device",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-************",
				Permissions: []Permission{
					{
						Scope:          "location_group",
						ScopeID:        "location-1",
						OrganizationID: "org-1",
						Permissions:    []string{"location_group_view_devices"},
					},
				},
			},
			origID:              789,
			requiredPermissions: []string{"location_group_view_devices"},
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-789",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{},
				LocationGroupIDs: []string{"location-1"},
			},
			expectedDeviceID: "device-uuid-789",
			expectError:      false,
		},
		{
			name: "user_without_permission_cannot_access_device",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-************",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "org-2",
						OrganizationID: "org-2",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			origID:              123,
			requiredPermissions: []string{"org_view_devices"},
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-123",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{"group-1"},
				LocationGroupIDs: []string{},
			},
			expectedDeviceID: "",
			expectError:      false,
		},
		{
			name: "nil_database_returns_error",
			userPermissions: &UserPermissions{
				UserID:      "550e8400-e29b-41d4-a716-************",
				Permissions: []Permission{},
			},
			origID:              123,
			requiredPermissions: []string{"org_view_devices"},
			expectedDeviceID:    "",
			expectError:         true,
		},
		{
			name: "database_error_returns_error",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-************",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "org-1",
						OrganizationID: "org-1",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			origID:              123,
			requiredPermissions: []string{"org_view_devices"},
			mockError:           assert.AnError,
			expectedDeviceID:    "",
			expectError:         true,
		},
		{
			name: "default_permissions_used_when_none_specified",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-************",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "org-1",
						OrganizationID: "org-1",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			origID:              123,
			requiredPermissions: nil, // Will use default permissions
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-123",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{},
				LocationGroupIDs: []string{},
			},
			expectedDeviceID: "device-uuid-123",
			expectError:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var mockDB *dbexecutor.FakeDBExecutor

			if tt.name == "nil_database_returns_error" {
				// Test the nil database case directly
				var deviceID string
				var err error
				if tt.requiredPermissions == nil {
					deviceID, err = tt.userPermissions.CanAccessDeviceByOrigID(nil, tt.origID)
				} else {
					deviceID, err = tt.userPermissions.CanAccessDeviceByOrigID(nil, tt.origID, tt.requiredPermissions...)
				}
				assert.Error(t, err)
				assert.Equal(t, "", deviceID)
				assert.Contains(t, err.Error(), "pg is nil")
				return
			}

			mockDB = &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if deviceInfo, ok := dest.(*DeviceInfo); ok && tt.mockDeviceInfo != nil {
						*deviceInfo = *tt.mockDeviceInfo
					}
					return nil
				},
			}

			var deviceID string
			var err error
			if tt.requiredPermissions == nil {
				deviceID, err = tt.userPermissions.CanAccessDeviceByOrigID(mockDB, tt.origID)
			} else {
				deviceID, err = tt.userPermissions.CanAccessDeviceByOrigID(mockDB, tt.origID, tt.requiredPermissions...)
			}

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedDeviceID, deviceID)
			}
		})
	}
}

// TestGetUserPermissionsWithGetAllUserPermissionsError tests that GetUserPermissions properly handles errors from getAllUserPermissions
func TestGetUserPermissionsWithGetAllUserPermissionsError(t *testing.T) {
	// Create mock database executor that returns an error
	mockDB := &dbexecutor.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			return fmt.Errorf("database connection failed")
		},
	}

	// Test the function with valid UUID but database error
	permissions, err := GetUserPermissions(mockDB, "550e8400-e29b-41d4-a716-************")
	assert.Error(t, err, "Should return error when getAllUserPermissions fails")
	assert.Nil(t, permissions, "Permissions should be nil when getAllUserPermissions fails")
	assert.Contains(t, err.Error(), "failed to get user permissions", "Error should mention failed to get user permissions")
	assert.Contains(t, err.Error(), "database connection failed", "Error should contain original error message")
}

// TestGetAllUserPermissionsQueryError tests that getAllUserPermissions properly handles errors from QueryGenericSlice
func TestGetAllUserPermissionsQueryError(t *testing.T) {
	// Create mock database executor that returns an error
	mockDB := &dbexecutor.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			return fmt.Errorf("query execution failed")
		},
	}

	// Test the function with valid UUID but query error
	permissions, err := getAllUserPermissions(mockDB, "550e8400-e29b-41d4-a716-************")
	assert.Error(t, err, "Should return error when QueryGenericSlice fails")
	assert.Nil(t, permissions, "Permissions should be nil when QueryGenericSlice fails")
	assert.Contains(t, err.Error(), "failed to query user permissions", "Error should mention failed to query user permissions")
	assert.Contains(t, err.Error(), "query execution failed", "Error should contain original error message")
}
