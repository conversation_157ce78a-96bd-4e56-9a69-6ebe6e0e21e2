package authorizer

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/jwttokens"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/mocks/dbexecutor"
)

func TestAuthorize(t *testing.T) {
	// Save and restore globals
	origValidate := jwttokens.ValidateJJwtToken
	origGetConns := connect.GetConnections
	origGetUser := getUserFromToken
	origValidEP := validEndPointRequestFromRole
	defer func() {
		jwttokens.ValidateJJwtToken = origValidate
		connect.GetConnections = origGetConns
		getUserFromToken = origGetUser
		validEndPointRequestFromRole = origValidEP
	}()

	tests := []struct {
		name          string
		headerToken   string
		validateErr   error
		connsErr      error
		userID        string
		permissions   []Permission
		expiresIn     time.Duration
		getUserErr    error
		allowEndpoint bool
		wantErr       error
	}{
		{
			name:        "missing header",
			headerToken: "",
			wantErr:     ErrUnauthorized,
		},
		{
			name:        "invalid JWT",
			headerToken: "bad",
			validateErr: errors.New("bad sig"),
			wantErr:     ErrUnauthorized,
		},
		{
			name:        "connection failure",
			headerToken: "tok",
			validateErr: nil,
			connsErr:    errors.New("no db"),
			wantErr:     ErrInternal,
		},
		{
			name:        "token not found",
			headerToken: "tok",
			validateErr: nil,
			getUserErr:  sql.ErrNoRows,
			wantErr:     ErrUnauthorized,
		},
		{
			name:        "generic database error",
			headerToken: "tok",
			validateErr: nil,
			getUserErr:  errors.New("database error"),
			wantErr:     ErrInternal,
		},
		{
			name:        "token expired",
			headerToken: "tok",
			validateErr: nil,
			userID:      "550e8400-e29b-41d4-a716-446655440001",
			permissions: []Permission{},
			expiresIn:   -time.Hour,
			getUserErr:  nil,
			wantErr:     ErrForbidden,
		},
		{
			name:        "forbidden by permissions",
			headerToken: "tok",
			validateErr: nil,
			userID:      "550e8400-e29b-41d4-a716-446655440002",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440003",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440003",
					Permissions:    []string{"org_view_users"}, // Missing org_view_devices
				},
			},
			expiresIn:     time.Hour,
			getUserErr:    nil,
			allowEndpoint: false,
			wantErr:       ErrForbidden,
		},
		{
			name:        "success",
			headerToken: "tok",
			validateErr: nil,
			userID:      "550e8400-e29b-41d4-a716-446655440003",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440004",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440004",
					Permissions:    []string{"org_view_devices", "org_manage_devices"},
				},
			},
			expiresIn:     time.Hour,
			getUserErr:    nil,
			allowEndpoint: true,
			wantErr:       nil,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			// stub JWT validation
			jwttokens.ValidateJJwtToken = func(token string) (*jwttokens.JwtToken, *jwttokens.UserPermissions, error) {
				return nil, nil, tc.validateErr
			}

			// stub connections (match real signature)
			connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
				if tc.connsErr != nil {
					return nil, tc.connsErr
				}
				conns := mocks.FakeConns()
				return conns, nil
			}

			// stub getUserFromToken to return UserPermissions
			getUserFromToken = func(pg connect.DatabaseExecutor, jwtToken string) (*UserPermissions, error) {
				if tc.getUserErr != nil {
					// Wrap errors properly like the real function does
					if errors.Is(tc.getUserErr, sql.ErrNoRows) {
						return nil, fmt.Errorf("%w: token not found or the user is disabled", ErrUnauthorized)
					}
					return nil, fmt.Errorf("%w: %v", ErrInternal, tc.getUserErr)
				}
				return &UserPermissions{
					UserID:      tc.userID,
					Permissions: tc.permissions,
				}, nil
			}

			// stub endpoint check
			validEndPointRequestFromRole = func(userPermissions *UserPermissions, r *http.Request) bool {
				return tc.allowEndpoint
			}

			// build request
			req := httptest.NewRequest("GET", "/data/v2/device", nil)
			if tc.headerToken != "" {
				req.Header.Set(headerJWT, tc.headerToken)
			}

			// invoke
			ctx, err := Authorize(context.Background(), req)

			// assert error
			if tc.wantErr != nil {
				if !errors.Is(err, tc.wantErr) {
					t.Fatalf("got err %v, want %v", err, tc.wantErr)
				}
				return
			}

			// success path
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			userPerms, ok := UserPermissionsFromContext(ctx)
			if !ok {
				t.Fatal("UserPermissions not in context")
			}
			if userPerms.UserID != tc.userID {
				t.Errorf("got UserID %s, want %s", userPerms.UserID, tc.userID)
			}
		})
	}
}

func TestValidEndPointRequestFromRole(t *testing.T) {
	tests := []struct {
		name        string
		permissions []Permission
		path        string
		want        bool
	}{
		{
			name: "admin with org_view_devices can access device endpoint",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440001",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions:    []string{"org_view_devices", "org_manage_devices"},
				},
			},
			path: "/data/v2/device/123",
			want: true,
		},
		{
			name: "device group permission allows device access",
			permissions: []Permission{
				{
					Scope:          "device_group",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440002",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions:    []string{"device_group_manage_devices"},
				},
			},
			path: "/api/v3/user/instruction",
			want: true,
		},
		{
			name: "no device permissions denies device access",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440001",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions:    []string{"org_view_users", "org_manage_users"},
				},
			},
			path: "/data/v2/device",
			want: false,
		},
		{
			name: "user permissions allow profile access",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "550e8400-e29b-41d4-a716-446655440001",
					OrganizationID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions:    []string{"org_view_users"},
				},
			},
			path: "/user/v2/profile",
			want: true,
		},
		{
			name:        "no permissions for unprotected endpoint allows access",
			permissions: []Permission{},
			path:        "/some/unprotected/endpoint",
			want:        true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			userPerms := &UserPermissions{
				UserID:      "550e8400-e29b-41d4-a716-************",
				Permissions: tc.permissions,
			}
			req := httptest.NewRequest("GET", tc.path, nil)
			got := validEndPointRequestFromRole(userPerms, req)
			if got != tc.want {
				t.Errorf("validEndPointRequestFromRole(%+v, %q) = %v; want %v", tc.permissions, tc.path, got, tc.want)
			}
		})
	}
}

// TestUserPermissions_hasAnyPermissionFromList tests the hasAnyPermissionFromList method
func TestUserPermissions_hasAnyPermissionFromList(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "550e8400-e29b-41d4-a716-************",
		Permissions: []Permission{
			{
				Scope:          "org",
				ScopeID:        "org-1",
				OrganizationID: "org-1",
				Permissions:    []string{"org_view_users", "org_manage_users"},
			},
			{
				Scope:          "device_group",
				ScopeID:        "group-1",
				OrganizationID: "org-1",
				Permissions:    []string{"device_group_manage_devices"},
			},
		},
	}

	tests := []struct {
		name                string
		requiredPermissions []string
		expected            bool
	}{
		{
			name:                "has_one_of_required_permissions",
			requiredPermissions: []string{"org_view_users", "some_other_permission"},
			expected:            true,
		},
		{
			name:                "has_all_required_permissions",
			requiredPermissions: []string{"org_view_users", "org_manage_users"},
			expected:            true,
		},
		{
			name:                "does_not_have_any_required_permissions",
			requiredPermissions: []string{"nonexistent_permission", "another_nonexistent"},
			expected:            false,
		},
		{
			name:                "empty_required_permissions_returns_false",
			requiredPermissions: []string{},
			expected:            false,
		},
		{
			name:                "has_device_group_permission",
			requiredPermissions: []string{"device_group_manage_devices"},
			expected:            true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := userPermissions.hasAnyPermissionFromList(tt.requiredPermissions)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestUserPermissions_getDevicesForPermissionScopes tests the getDevicesForPermissionScopes method
func TestUserPermissions_getDevicesForPermissionScopes(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "550e8400-e29b-41d4-a716-************",
	}

	tests := []struct {
		name                string
		permissions         []Permission
		requiredPermissions []string
		mockDevices         []string
		mockError           error
		expectedDevices     []string
		expectError         bool
	}{
		{
			name: "org_permissions_get_devices",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "org-1",
					OrganizationID: "org-1",
					Permissions:    []string{"org_view_devices"},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDevices:         []string{"device-1", "device-2"},
			expectedDevices:     []string{"device-1", "device-2"},
			expectError:         false,
		},
		{
			name: "device_group_permissions_get_devices",
			permissions: []Permission{
				{
					Scope:          "device_group",
					ScopeID:        "group-1",
					OrganizationID: "org-1",
					Permissions:    []string{"device_group_view_devices"},
				},
			},
			requiredPermissions: []string{"device_group_view_devices"},
			mockDevices:         []string{"device-3", "device-4"},
			expectedDevices:     []string{"device-3", "device-4"},
			expectError:         false,
		},
		{
			name: "location_group_permissions_get_devices",
			permissions: []Permission{
				{
					Scope:          "location_group",
					ScopeID:        "location-1",
					OrganizationID: "org-1",
					Permissions:    []string{"location_group_view_devices"},
				},
			},
			requiredPermissions: []string{"location_group_view_devices"},
			mockDevices:         []string{"device-5", "device-6"},
			expectedDevices:     []string{"device-5", "device-6"},
			expectError:         false,
		},
		{
			name: "mixed_permissions_get_combined_devices",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "org-1",
					OrganizationID: "org-1",
					Permissions:    []string{"org_view_devices"},
				},
				{
					Scope:          "device_group",
					ScopeID:        "group-1",
					OrganizationID: "org-1",
					Permissions:    []string{"device_group_view_devices"},
				},
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			mockDevices:         []string{"device-1", "device-2", "device-3"},
			expectedDevices:     []string{"device-1", "device-2", "device-3"},
			expectError:         false,
		},
		{
			name: "no_matching_permissions_returns_empty",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "org-1",
					OrganizationID: "org-1",
					Permissions:    []string{"org_view_users"},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDevices:         []string{},
			expectedDevices:     []string{},
			expectError:         false,
		},
		{
			name:                "empty_permissions_returns_empty",
			permissions:         []Permission{},
			requiredPermissions: []string{"org_view_devices"},
			mockDevices:         []string{},
			expectedDevices:     []string{},
			expectError:         false,
		},
		{
			name: "database_error_returns_error",
			permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "org-1",
					OrganizationID: "org-1",
					Permissions:    []string{"org_view_devices"},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockError:           fmt.Errorf("database connection failed"),
			expectedDevices:     nil,
			expectError:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					// The actual function expects a slice of structs with DeviceID field
					if deviceIds, ok := dest.(*[]struct {
						DeviceID string `db:"device_id"`
					}); ok {
						*deviceIds = make([]struct {
							DeviceID string `db:"device_id"`
						}, len(tt.mockDevices))
						for i, deviceID := range tt.mockDevices {
							(*deviceIds)[i].DeviceID = deviceID
						}
					}
					return nil
				},
			}

			devices, err := userPermissions.getDevicesForPermissionScopes(mockDB, tt.permissions, tt.requiredPermissions)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedDevices, devices)
			}
		})
	}
}

// TestGetDeviceInfoByOrigID tests the getDeviceInfoByOrigID function
func TestGetDeviceInfoByOrigID(t *testing.T) {
	tests := []struct {
		name           string
		origID         int64
		mockDeviceInfo *DeviceInfo
		mockError      error
		expectError    bool
	}{
		{
			name:   "successful_device_info_retrieval",
			origID: 123,
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-123",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{"group-1", "group-2"},
				LocationGroupIDs: []string{"location-1"},
			},
			expectError: false,
		},
		{
			name:        "database_error_returns_error",
			origID:      456,
			mockError:   fmt.Errorf("device not found"),
			expectError: true,
		},
		{
			name:   "device_with_no_groups",
			origID: 789,
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-789",
				OrganizationID:   "org-2",
				DeviceGroupIDs:   []string{},
				LocationGroupIDs: []string{},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if deviceInfo, ok := dest.(*DeviceInfo); ok && tt.mockDeviceInfo != nil {
						*deviceInfo = *tt.mockDeviceInfo
					}
					return nil
				},
			}

			deviceInfo, err := getDeviceInfoByOrigID(mockDB, tt.origID)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, deviceInfo)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, deviceInfo)
				if tt.mockDeviceInfo != nil {
					assert.Equal(t, tt.mockDeviceInfo.DeviceID, deviceInfo.DeviceID)
					assert.Equal(t, tt.mockDeviceInfo.OrganizationID, deviceInfo.OrganizationID)
					assert.Equal(t, tt.mockDeviceInfo.DeviceGroupIDs, deviceInfo.DeviceGroupIDs)
					assert.Equal(t, tt.mockDeviceInfo.LocationGroupIDs, deviceInfo.LocationGroupIDs)
				}
			}
		})
	}
}

// TestGetDeviceInfo tests the getDeviceInfo function
func TestGetDeviceInfo(t *testing.T) {
	tests := []struct {
		name           string
		deviceID       string
		mockDeviceInfo *DeviceInfo
		mockError      error
		expectError    bool
	}{
		{
			name:     "successful_device_info_retrieval",
			deviceID: "device-uuid-123",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-123",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{"group-1", "group-2"},
				LocationGroupIDs: []string{"location-1"},
			},
			expectError: false,
		},
		{
			name:        "device_not_found_returns_error",
			deviceID:    "nonexistent-device",
			mockError:   fmt.Errorf("device not found: nonexistent-device"),
			expectError: true,
		},
		{
			name:     "device_with_no_groups",
			deviceID: "device-uuid-789",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-789",
				OrganizationID:   "org-2",
				DeviceGroupIDs:   []string{},
				LocationGroupIDs: []string{},
			},
			expectError: false,
		},
		{
			name:        "database_connection_error",
			deviceID:    "device-uuid-456",
			mockError:   fmt.Errorf("database connection failed"),
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if deviceInfo, ok := dest.(*DeviceInfo); ok && tt.mockDeviceInfo != nil {
						*deviceInfo = *tt.mockDeviceInfo
					}
					return nil
				},
			}

			deviceInfo, err := getDeviceInfo(mockDB, tt.deviceID)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, deviceInfo)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, deviceInfo)
				if tt.mockDeviceInfo != nil {
					assert.Equal(t, tt.mockDeviceInfo.DeviceID, deviceInfo.DeviceID)
					assert.Equal(t, tt.mockDeviceInfo.OrganizationID, deviceInfo.OrganizationID)
					assert.Equal(t, tt.mockDeviceInfo.DeviceGroupIDs, deviceInfo.DeviceGroupIDs)
					assert.Equal(t, tt.mockDeviceInfo.LocationGroupIDs, deviceInfo.LocationGroupIDs)
				}
			}
		})
	}
}

// TestUserPermissions_CanAccessDevice_Additional tests additional scenarios for CanAccessDevice
func TestUserPermissions_CanAccessDevice_Additional(t *testing.T) {
	tests := []struct {
		name            string
		userPermissions *UserPermissions
		deviceId        string
		mockDeviceInfo  *DeviceInfo
		mockError       error
		requiredPerms   []string
		expectedAccess  bool
		expectError     bool
	}{
		{
			name: "location_group_permission_grants_access",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "location_group",
						ScopeID:        "location-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"location_group_view_devices"},
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{},
				LocationGroupIDs: []string{"location-1", "location-2"},
			},
			requiredPerms:  []string{"location_group_view_devices"},
			expectedAccess: true,
			expectError:    false,
		},
		{
			name: "user_without_required_permission_type_denied",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_users"}, // Wrong permission type
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{"group-1"},
				LocationGroupIDs: []string{},
			},
			requiredPerms:  []string{"org_view_devices"},
			expectedAccess: false,
			expectError:    false,
		},
		{
			name: "database_error_during_device_lookup",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			deviceId:       "device-1",
			mockError:      fmt.Errorf("database connection failed"),
			requiredPerms:  []string{"org_view_devices"},
			expectedAccess: false,
			expectError:    true,
		},
		{
			name: "multiple_permission_scopes_first_matches",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-2", // Different group
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{"group-1"}, // Different from permission group
				LocationGroupIDs: []string{},
			},
			requiredPerms:  []string{"org_view_devices", "device_group_view_devices"},
			expectedAccess: true, // Should match on org permission
			expectError:    false,
		},
		{
			name: "default_permissions_when_none_specified",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{},
				LocationGroupIDs: []string{},
			},
			requiredPerms:  nil, // Will use default permissions
			expectedAccess: true,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if deviceInfo, ok := dest.(*DeviceInfo); ok && tt.mockDeviceInfo != nil {
						*deviceInfo = *tt.mockDeviceInfo
					}
					return nil
				},
			}

			var canAccess bool
			var err error
			if tt.requiredPerms == nil {
				canAccess, err = tt.userPermissions.CanAccessDevice(mockDB, tt.deviceId)
			} else {
				canAccess, err = tt.userPermissions.CanAccessDevice(mockDB, tt.deviceId, tt.requiredPerms...)
			}

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedAccess, canAccess)
			}
		})
	}
}

// TestGetDeviceInfoByOrigID_Additional tests additional scenarios for getDeviceInfoByOrigID
func TestGetDeviceInfoByOrigID_Additional(t *testing.T) {
	tests := []struct {
		name           string
		origID         int64
		mockDeviceInfo *DeviceInfo
		mockError      error
		expectError    bool
	}{
		{
			name:   "device_with_multiple_groups",
			origID: 12345,
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-12345",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{"group-1", "group-2", "group-3"},
				LocationGroupIDs: []string{"location-1", "location-2"},
			},
			expectError: false,
		},
		{
			name:        "device_not_found_by_origID",
			origID:      99999,
			mockError:   fmt.Errorf("device not found with origID: 99999"),
			expectError: true,
		},
		{
			name:   "zero_origID",
			origID: 0,
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-uuid-0",
				OrganizationID:   "org-1",
				DeviceGroupIDs:   []string{},
				LocationGroupIDs: []string{},
			},
			expectError: false,
		},
		{
			name:        "database_timeout_error",
			origID:      456,
			mockError:   fmt.Errorf("database query timeout"),
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if deviceInfo, ok := dest.(*DeviceInfo); ok && tt.mockDeviceInfo != nil {
						*deviceInfo = *tt.mockDeviceInfo
					}
					return nil
				},
			}

			deviceInfo, err := getDeviceInfoByOrigID(mockDB, tt.origID)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, deviceInfo)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, deviceInfo)
				if tt.mockDeviceInfo != nil {
					assert.Equal(t, tt.mockDeviceInfo.DeviceID, deviceInfo.DeviceID)
					assert.Equal(t, tt.mockDeviceInfo.OrganizationID, deviceInfo.OrganizationID)
					assert.Equal(t, tt.mockDeviceInfo.DeviceGroupIDs, deviceInfo.DeviceGroupIDs)
					assert.Equal(t, tt.mockDeviceInfo.LocationGroupIDs, deviceInfo.LocationGroupIDs)
				}
			}
		})
	}
}

// TestGetUserFromToken tests the getUserFromToken function to achieve 100% coverage
func TestGetUserFromToken(t *testing.T) {
	tests := []struct {
		name        string
		token       string
		mockSetup   func(*dbexecutor.FakeDBExecutor)
		expectError bool
		errorMsg    string
	}{
		{
			name:  "successful token validation",
			token: "valid-token",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful token query
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if userInfo, ok := dest.(*dbUserInfo); ok {
						userInfo.UserID = "550e8400-e29b-41d4-a716-************"
						userInfo.ExpirationUTC = time.Now().Add(time.Hour) // Valid future time
					}
					return nil
				}
				// Mock successful permissions query
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					if perms, ok := dest.(*[]Permission); ok {
						*perms = []Permission{
							{
								Scope:          "org",
								ScopeID:        "550e8400-e29b-41d4-a716-446655440001",
								OrganizationID: "550e8400-e29b-41d4-a716-446655440001",
								Permissions:    []string{"org_view_devices"},
							},
						}
					}
					return nil
				}
			},
			expectError: false,
		},
		{
			name:  "token not found",
			token: "invalid-token",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectError: true,
			errorMsg:    "token not found or the user is disabled",
		},
		{
			name:  "database error",
			token: "some-token",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return fmt.Errorf("database connection error")
				}
			},
			expectError: true,
			errorMsg:    "database connection error",
		},
		{
			name:  "expired token",
			token: "expired-token",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if userInfo, ok := dest.(*dbUserInfo); ok {
						userInfo.UserID = "550e8400-e29b-41d4-a716-************"
						userInfo.ExpirationUTC = time.Now().Add(-time.Hour) // Expired time
					}
					return nil
				}
			},
			expectError: true,
			errorMsg:    "token expired",
		},
		{
			name:  "permissions query error",
			token: "valid-token",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful token query
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if userInfo, ok := dest.(*dbUserInfo); ok {
						userInfo.UserID = "550e8400-e29b-41d4-a716-************"
						userInfo.ExpirationUTC = time.Now().Add(time.Hour)
					}
					return nil
				}
				// Mock permissions query error
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return fmt.Errorf("permissions query failed")
				}
			},
			expectError: true,
			errorMsg:    "permissions query failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			result, err := getUserFromToken(mockDB, tt.token)

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "550e8400-e29b-41d4-a716-************", result.UserID)
			}
		})
	}
}

// TestGetAuthorizedDevices_ErrorHandling tests error scenarios to reach 100% coverage
func TestGetAuthorizedDevices_ErrorHandling(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "user-123",
		Permissions: []Permission{
			{
				Scope:          "org",
				ScopeID:        "org-123",
				OrganizationID: "org-123",
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	t.Run("database error in getDevicesForPermissionScopes", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
				return fmt.Errorf("database connection failed")
			},
		}

		devices, err := userPermissions.GetAuthorizedDevices(mockDB)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get devices for permission scopes")
		assert.Nil(t, devices)
	})
}

// TestGetAuthorizedDevicesByOrganization_ErrorHandling tests error scenarios to reach 100% coverage
func TestGetAuthorizedDevicesByOrganization_ErrorHandling(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "user-123",
		Permissions: []Permission{
			{
				Scope:          "org",
				ScopeID:        "org-123",
				OrganizationID: "org-123",
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	t.Run("database error in getDevicesForPermissionScopes", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
				return fmt.Errorf("database connection failed")
			},
		}

		devices, err := userPermissions.GetAuthorizedDevicesByOrganization(mockDB, "org-123")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get devices for permission scopes in org org-123")
		assert.Nil(t, devices)
	})
}

// TestCanAccessDeviceByOrigID_ErrorHandling tests error scenarios to reach 100% coverage
func TestCanAccessDeviceByOrigID_ErrorHandling(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "user-123",
		Permissions: []Permission{
			{
				Scope:          "location_group",
				ScopeID:        "lg-123",
				OrganizationID: "org-123",
				Permissions:    []string{"location_group_view_devices"},
			},
		},
	}

	t.Run("device has location group access", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				if deviceInfo, ok := dest.(*DeviceInfo); ok {
					deviceInfo.DeviceID = "device-123"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{"dg-456"}
					deviceInfo.LocationGroupIDs = []string{"lg-123"}
				}
				return nil
			},
		}

		deviceID, err := userPermissions.CanAccessDeviceByOrigID(mockDB, 12345)
		assert.NoError(t, err)
		assert.Equal(t, "device-123", deviceID)
	})

	t.Run("user has permissions but not required permissions", func(t *testing.T) {
		// User has multiple permissions - first has wrong permission, second has right permission
		// This ensures the continue statement is executed for the first permission
		userPermsWithMixedPermissions := &UserPermissions{
			UserID: "user-123",
			Permissions: []Permission{
				{
					Scope:          "org",
					ScopeID:        "org-999", // Different org
					OrganizationID: "org-999",
					Permissions:    []string{"org_manage_users"}, // Wrong permission - doesn't include device viewing
				},
				{
					Scope:          "org",
					ScopeID:        "org-123", // Correct org
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"}, // Correct permission
				},
			},
		}

		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				if deviceInfo, ok := dest.(*DeviceInfo); ok {
					deviceInfo.DeviceID = "device-123"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{"dg-456"}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			},
		}

		// This should trigger the continue statement for the first permission (wrong permission)
		// and then grant access for the second permission (correct permission)
		deviceID, err := userPermsWithMixedPermissions.CanAccessDeviceByOrigID(mockDB, 12345)
		assert.NoError(t, err)
		assert.Equal(t, "device-123", deviceID) // Should return device ID as access granted by second permission
	})
}

// TestGetDeviceInfo_ErrorHandling tests error scenarios to reach 100% coverage
func TestGetDeviceInfo_ErrorHandling(t *testing.T) {
	t.Run("device not found", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows
			},
		}

		deviceInfo, err := getDeviceInfo(mockDB, "nonexistent-device")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "device not found: nonexistent-device")
		assert.Nil(t, deviceInfo)
	})
}

// TestGetDeviceInfoByOrigID_ErrorHandling tests error scenarios to reach 100% coverage
func TestGetDeviceInfoByOrigID_ErrorHandling(t *testing.T) {
	t.Run("device not found by origID", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows
			},
		}

		deviceInfo, err := getDeviceInfoByOrigID(mockDB, 99999)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "device not found with origID: 99999")
		assert.Nil(t, deviceInfo)
	})
}

// TestGetUserPermissions_ErrorHandling tests error scenarios to reach 100% coverage
func TestGetUserPermissions_ErrorHandling(t *testing.T) {
	t.Run("invalid UUID format", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{}

		permissions, err := GetUserPermissions(mockDB, "invalid-uuid")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid user ID format")
		assert.Nil(t, permissions)
	})

	t.Run("database error in getAllUserPermissions", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
				return fmt.Errorf("database query failed")
			},
		}

		permissions, err := GetUserPermissions(mockDB, "550e8400-e29b-41d4-a716-************")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get user permissions")
		assert.Nil(t, permissions)
	})
}

// TestGetAllUserPermissions_ErrorHandling tests error scenarios to reach 100% coverage
func TestGetAllUserPermissions_ErrorHandling(t *testing.T) {
	t.Run("database query error", func(t *testing.T) {
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
				return fmt.Errorf("complex query failed")
			},
		}

		permissions, err := getAllUserPermissions(mockDB, "550e8400-e29b-41d4-a716-************")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to query user permissions")
		assert.Nil(t, permissions)
	})
}

// Test_GetEligibleUsersForDevice tests the GetEligibleUsersForDevice function
func Test_GetEligibleUsersForDevice(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                string
		deviceID            string
		requiredPermissions []string
		setupMockFn         func() *dbexecutor.FakeDBExecutor
		expectedUsers       []User
		expectedErr         error
		wantErr             bool
		expectedQueryCalls  int
	}{
		{
			name:                "success_with_multiple_users",
			deviceID:            "device-123",
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			setupMockFn: func() *dbexecutor.FakeDBExecutor {
				mockDB := &dbexecutor.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query with multiple users
					users := dest.(*[]User)
					*users = []User{
						{ID: "user-1", Mobile: "+5551234567", IanaTimezone: "UTC"},
						{ID: "user-2", Mobile: "+5559876543", IanaTimezone: "America/Chicago"},
					}
					return nil
				}
				return mockDB
			},
			expectedUsers: []User{
				{ID: "user-1", Mobile: "+5551234567", IanaTimezone: "UTC"},
				{ID: "user-2", Mobile: "+5559876543", IanaTimezone: "America/Chicago"},
			},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
		{
			name:                "success_with_no_users_found",
			deviceID:            "device-456",
			requiredPermissions: []string{"org_view_devices"},
			setupMockFn: func() *dbexecutor.FakeDBExecutor {
				mockDB := &dbexecutor.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query but no users found
					users := dest.(*[]User)
					*users = []User{} // Empty slice
					return nil
				}
				return mockDB
			},
			expectedUsers:      []User{},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
		{
			name:                "database_error",
			deviceID:            "device-789",
			requiredPermissions: []string{"org_view_devices"},
			setupMockFn: func() *dbexecutor.FakeDBExecutor {
				mockDB := &dbexecutor.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
				return mockDB
			},
			expectedUsers:      nil,
			expectedErr:        nil,
			wantErr:            true,
			expectedQueryCalls: 1,
		},
		{
			name:                "single_user_found",
			deviceID:            "device-single",
			requiredPermissions: []string{"device_group_view_devices"},
			setupMockFn: func() *dbexecutor.FakeDBExecutor {
				mockDB := &dbexecutor.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query with single user
					users := dest.(*[]User)
					*users = []User{
						{ID: "9a3b5c7d-1e2f-4a8b-9c3d-5e6f7a8b9c0d", Mobile: "+5551234567", IanaTimezone: "UTC"},
					}
					return nil
				}
				return mockDB
			},
			expectedUsers: []User{
				{ID: "9a3b5c7d-1e2f-4a8b-9c3d-5e6f7a8b9c0d", Mobile: "+5551234567", IanaTimezone: "UTC"},
			},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
		{
			name:                "query_generic_slice_call_limit_error",
			deviceID:            "device-limit",
			requiredPermissions: []string{"org_view_devices"},
			setupMockFn: func() *dbexecutor.FakeDBExecutor {
				mockDB := &dbexecutor.FakeDBExecutor{}
				mockDB.EnableFailAfter = true
				mockDB.QueryGenericSliceCallFailAfter = 0
				return mockDB
			},
			expectedUsers:      nil,
			expectedErr:        nil,
			wantErr:            true,
			expectedQueryCalls: 1,
		},
		{
			name:                "default_permissions_when_none_provided",
			deviceID:            "device-default",
			requiredPermissions: []string{},
			setupMockFn: func() *dbexecutor.FakeDBExecutor {
				mockDB := &dbexecutor.FakeDBExecutor{}
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful query
					users := dest.(*[]User)
					*users = []User{}
					return nil
				}
				return mockDB
			},
			expectedUsers:      []User{},
			expectedErr:        nil,
			wantErr:            false,
			expectedQueryCalls: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := tt.setupMockFn()

			// Execute function under test
			ctx := context.Background()
			result, err := GetEligibleUsersForDevice(ctx, mockDB, tt.deviceID, tt.requiredPermissions...)

			// Assert error conditions
			if tt.wantErr {
				assert.Error(t, err, "should return error")
				if tt.expectedErr != nil {
					assert.ErrorIs(t, err, tt.expectedErr, "should return expected error type")
				}
				assert.Nil(t, result, "should return nil users on error")
			} else {
				assert.NoError(t, err, "should not return error")
				assert.Equal(t, tt.expectedUsers, result, "should return expected users")
			}

			// Assert database interaction
			assert.Equal(t, tt.expectedQueryCalls, mockDB.QueryGenericSliceCallCount, "should call QueryGenericSlice expected number of times")
		})
	}
}

// Test_GetEligibleUsersForDevice_query_validation tests parameter passing and function behavior
func Test_GetEligibleUsersForDevice_query_validation(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                string
		deviceID            string
		requiredPermissions []string
		expectedArgs        []interface{}
		expectedBehavior    func(*testing.T, string, []interface{})
	}{
		{
			name:                "basic_parameter_validation",
			deviceID:            "test-device-123",
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			expectedArgs: []interface{}{
				"test-device-123",
				pq.Array([]string{"org_view_devices", "device_group_view_devices"}),
			},
			expectedBehavior: func(t *testing.T, query string, args []interface{}) {
				// Verify query is not empty and looks like SQL
				assert.NotEmpty(t, query, "query should not be empty")
				assert.True(t, len(args) >= 2, "should have at least device ID and permissions arguments")

				// Verify device ID is passed correctly
				assert.Equal(t, "test-device-123", args[0], "should pass correct device ID")

				// Verify permissions are passed correctly (handle pq.Array type)
				if len(args) > 1 {
					// pq.Array returns a driver.Valuer, so we need to check the underlying value
					permsArray := args[1]
					assert.NotNil(t, permsArray, "permissions should not be nil")

					// Try to extract the actual value from the pq.Array
					if valuer, ok := permsArray.(driver.Valuer); ok {
						value, err := valuer.Value()
						assert.NoError(t, err, "should be able to get value from pq.Array")
						assert.NotNil(t, value, "value should not be nil")

						// pq.Array returns a string representation of the PostgreSQL array
						if strValue, ok := value.(string); ok {
							// The string should contain our permissions in PostgreSQL array format
							expectedPerms := []string{"org_view_devices", "device_group_view_devices"}
							for _, perm := range expectedPerms {
								assert.Contains(t, strValue, perm, "should contain permission: %s", perm)
							}
						} else {
							t.Errorf("expected string, got %T", value)
						}
					} else {
						t.Errorf("expected driver.Valuer, got %T", permsArray)
					}
				}
			},
		},
		{
			name:                "default_permissions_behavior",
			deviceID:            "test-device-456",
			requiredPermissions: []string{},
			expectedArgs: []interface{}{
				"test-device-456",
				pq.Array([]string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}),
			},
			expectedBehavior: func(t *testing.T, query string, args []interface{}) {
				// Verify default permissions are used when none provided
				assert.NotEmpty(t, query, "query should not be empty")
				assert.True(t, len(args) >= 2, "should have at least device ID and permissions arguments")

				// Verify device ID is passed correctly
				assert.Equal(t, "test-device-456", args[0], "should pass correct device ID")

				// Verify default permissions are passed
				if len(args) > 1 {
					permsArray := args[1]
					assert.NotNil(t, permsArray, "permissions should not be nil")

					if valuer, ok := permsArray.(driver.Valuer); ok {
						value, err := valuer.Value()
						assert.NoError(t, err, "should be able to get value from pq.Array")
						assert.NotNil(t, value, "value should not be nil")

						if strValue, ok := value.(string); ok {
							expectedDefaults := []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
							for _, perm := range expectedDefaults {
								assert.Contains(t, strValue, perm, "should contain default permission: %s", perm)
							}
						} else {
							t.Errorf("expected string, got %T", value)
						}
					} else {
						t.Errorf("expected driver.Valuer, got %T", permsArray)
					}
				}
			},
		},
		{
			name:                "single_permission_behavior",
			deviceID:            "test-device-789",
			requiredPermissions: []string{"org_manage_devices"},
			expectedArgs: []interface{}{
				"test-device-789",
				pq.Array([]string{"org_manage_devices"}),
			},
			expectedBehavior: func(t *testing.T, query string, args []interface{}) {
				// Verify single permission is handled correctly
				assert.NotEmpty(t, query, "query should not be empty")
				assert.True(t, len(args) >= 2, "should have at least device ID and permissions arguments")

				// Verify device ID is passed correctly
				assert.Equal(t, "test-device-789", args[0], "should pass correct device ID")

				// Verify single permission is passed
				if len(args) > 1 {
					permsArray := args[1]
					assert.NotNil(t, permsArray, "permissions should not be nil")

					if valuer, ok := permsArray.(driver.Valuer); ok {
						value, err := valuer.Value()
						assert.NoError(t, err, "should be able to get value from pq.Array")
						assert.NotNil(t, value, "value should not be nil")

						if strValue, ok := value.(string); ok {
							assert.Contains(t, strValue, "org_manage_devices", "should contain single permission")
						} else {
							t.Errorf("expected string, got %T", value)
						}
					} else {
						t.Errorf("expected driver.Valuer, got %T", permsArray)
					}
				}
			},
		},
		{
			name:                "multiple_permissions_behavior",
			deviceID:            "test-device-multi",
			requiredPermissions: []string{"org_view_devices", "device_group_manage_devices", "location_group_view_devices"},
			expectedArgs: []interface{}{
				"test-device-multi",
				pq.Array([]string{"org_view_devices", "device_group_manage_devices", "location_group_view_devices"}),
			},
			expectedBehavior: func(t *testing.T, query string, args []interface{}) {
				// Verify multiple permissions are handled correctly
				assert.NotEmpty(t, query, "query should not be empty")
				assert.True(t, len(args) >= 2, "should have at least device ID and permissions arguments")

				// Verify device ID is passed correctly
				assert.Equal(t, "test-device-multi", args[0], "should pass correct device ID")

				// Verify multiple permissions are passed
				if len(args) > 1 {
					permsArray := args[1]
					assert.NotNil(t, permsArray, "permissions should not be nil")

					if valuer, ok := permsArray.(driver.Valuer); ok {
						value, err := valuer.Value()
						assert.NoError(t, err, "should be able to get value from pq.Array")
						assert.NotNil(t, value, "value should not be nil")

						if strValue, ok := value.(string); ok {
							expectedPerms := []string{"org_view_devices", "device_group_manage_devices", "location_group_view_devices"}
							for _, perm := range expectedPerms {
								assert.Contains(t, strValue, perm, "should contain permission: %s", perm)
							}
						} else {
							t.Errorf("expected string, got %T", value)
						}
					} else {
						t.Errorf("expected driver.Valuer, got %T", permsArray)
					}
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock to capture query and args
			var capturedQuery string
			var capturedArgs []interface{}

			mockDB := &dbexecutor.FakeDBExecutor{}
			mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
				capturedQuery = query
				capturedArgs = make([]interface{}, len(args))
				copy(capturedArgs, args)

				// Return empty users to complete the test
				users := dest.(*[]User)
				*users = []User{}
				return nil
			}

			// Execute function under test
			ctx := context.Background()
			result, err := GetEligibleUsersForDevice(ctx, mockDB, tt.deviceID, tt.requiredPermissions...)

			// Assert no error and empty result (since we're not testing actual data)
			assert.NoError(t, err, "should not return error")
			assert.NotNil(t, result, "should return result slice")
			assert.Empty(t, result, "should return empty result for test")

			// Verify database was called exactly once
			assert.Equal(t, 1, mockDB.QueryGenericSliceCallCount, "should call database exactly once")

			// Run behavior-specific validations
			tt.expectedBehavior(t, capturedQuery, capturedArgs)
		})
	}
}
