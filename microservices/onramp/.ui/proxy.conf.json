{"/foo": {"target": "http://localhost:8080", "secure": false, "changeOrigin": false, "xfwd": true}, "/gateway": {"target": "http://localhost:8080", "secure": false, "changeOrigin": false, "xfwd": true}, "/api": {"target": "http://localhost:8080", "secure": false, "changeOrigin": true, "xfwd": true, "logLevel": "debug"}, "/assets": {"target": "http://localhost:8080", "secure": false, "changeOrigin": false, "xfwd": true}, "/protected": {"target": "http://localhost:8080", "secure": false, "changeOrigin": false, "xfwd": true}, "/login": {"target": "http://localhost:8080", "secure": false, "changeOrigin": false, "xfwd": true}, "/logout": {"target": "http://localhost:8080", "secure": false, "changeOrigin": false, "xfwd": true}, "/callback": {"target": "http://localhost:8080", "secure": false, "changeOrigin": false, "xfwd": true}}