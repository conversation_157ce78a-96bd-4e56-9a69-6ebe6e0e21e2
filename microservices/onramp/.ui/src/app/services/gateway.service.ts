import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class GatewayService {
  constructor(private http: HttpClient) { }
  getGateway(): Observable<any[]> {
    return this.http.get<any[]>('/gateway').pipe(delay(500));
  }

  getOrganizations(): Observable<any[]> {
    return this.http.get<any[]>('/organizations').pipe(delay(500));
  }

  getConfigurations(): Observable<any[]> {
    return this.http.get<any[]>('/gateway-config').pipe(delay(500));
  }
}