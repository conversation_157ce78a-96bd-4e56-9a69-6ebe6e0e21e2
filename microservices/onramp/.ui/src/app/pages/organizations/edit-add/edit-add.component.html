<nz-modal [(nzVisible)]="isVisible" [nzTitle]="modalTitle" (nzOnCancel)="handleCancel()" [nzOkText]="'Save'"
  [nzFooter]="modalFooter" nzWidth="600px">
  <ng-template #modalTitle>
    <h3 style="margin-bottom: 0;">{{isDetail ? 'Organization Detail' : isEditMode ? 'Edit Organization' : 'Add
      Organization' }}</h3>
  </ng-template>
  <ng-container *nzModalContent>
    <nz-form *ngIf="!isDetail" [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSpan]="5" nzRequired>Name</nz-form-label>
        <nz-form-control [nzSpan]="18" nzErrorTip="Please Input Name.">
          <input nz-input id="new-organization-name" formControlName="name" placeholder="Enter Name" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5" nzRequired>Description</nz-form-label>
        <nz-form-control [nzSpan]="18" nzErrorTip="Please Input Description.">
          <input nz-input formControlName="description" placeholder="Enter Description" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item *ngIf="!isEditMode">
        <nz-form-label [nzSpan]="5" nzRequired>Organization Type</nz-form-label>
        <nz-form-control [nzSpan]="18" nzErrorTip="Please Select Organization Type.">
          <nz-select formControlName="orgtypeidentifier" nzPlaceHolder="Select Organization Type">
            <nz-option nzValue="municipality" nzLabel="Municipality"></nz-option>
            <nz-option nzValue="oem" nzLabel="OEM"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </nz-form>
    <nz-form *ngIf="isDetail" [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Name</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="name" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Description</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="description" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Organization Type</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="orgtypeidentifier" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Date Created</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="createdat" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Date Updated</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="updatedat" />
        </nz-form-control>
      </nz-form-item>
    </nz-form>
  </ng-container>
  <ng-template #modalFooter>
    <button nz-button nzType="default" (click)="handleCancel()">Cancel</button>
    <button *ngIf="!isDetail" class="btn-submit" nz-button nzType="primary" (click)="handleSave()">Save</button>
  </ng-template>
</nz-modal>