import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Organization } from '../../../models/organizations.model';
// import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-edit-add',
  standalone: false,
  templateUrl: './edit-add.component.html',
  styleUrl: './edit-add.component.css'
})
export class EditAddComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() isDetail = false;
  @Input() data: Organization | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  form: FormGroup;

  constructor(
    private fb: FormBuilder,
    // private datePipe: DatePipe
  ) {
    this.form = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      description: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      orgtypeidentifier: ['', [
        Validators.required
      ]],
      id: [''],
      createdat: [''],
      updatedat: ['']
    });

    // Auto-trim name and description fields on blur/submit only
    // Note: We don't auto-trim on valueChanges to allow normal typing with spaces
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isEditMode'] || changes['isDetail'] || changes['data']) {
      if (this.data && (this.isEditMode || this.isDetail)) {
        // Use the existing data as-is without aggressive trimming for display
        this.form.reset({
          id: this.data?.id,
          name: this.data?.name || '',
          description: this.data?.description || '',
          orgtypeidentifier: this.data?.orgtypeidentifier,
          createdat: this.data?.createdat,
          updatedat: this.data?.updatedat,
        }, { emitEvent: false });

        this.form.get('name')?.enable();
        this.form.get('description')?.enable();
        this.form.get('orgtypeidentifier')?.enable();

        // In edit mode, make orgtypeidentifier read-only
        if (this.isEditMode) {
          this.form.get('orgtypeidentifier')?.disable();
        }

        this.form.updateValueAndValidity();
      } else {
        this.form.reset({
          id: '',
          name: '',
          description: '',
          orgtypeidentifier: ''
        });
        this.form.get('name')?.enable();
        this.form.get('description')?.enable();
        this.form.get('orgtypeidentifier')?.enable();
      }

      if (this.data && this.isDetail) {
        this.form.get('name')?.disable();
        this.form.get('description')?.disable();
        this.form.get('orgtypeidentifier')?.disable();
        this.form.get('createdat')?.disable();
        this.form.get('updatedat')?.disable();
      }
    }
  }

  handleSave(): void {
    if (this.form.valid) {
      const formValue = this.form.value;

      // Clean up name and description by trimming and normalizing spaces
      const cleanName = formValue.name ? formValue.name.trim().replace(/\s+/g, ' ') : '';
      const cleanDescription = formValue.description ? formValue.description.trim().replace(/\s+/g, ' ') : '';

      // Validate that cleaned values are not empty
      if (!cleanName || !cleanDescription) {
        // Mark appropriate fields as invalid
        if (!cleanName) {
          this.form.get('name')?.setErrors({ 'required': true });
          this.form.get('name')?.markAsDirty();
        }
        if (!cleanDescription) {
          this.form.get('description')?.setErrors({ 'required': true });
          this.form.get('description')?.markAsDirty();
        }
        return;
      }

      // For edit mode, don't include orgtypeidentifier in the payload
      if (this.isEditMode) {
        const updatePayload = {
          name: cleanName,
          description: cleanDescription
        };
        this.confirm.emit(updatePayload);
      } else {
        // For create mode, include all required fields
        const createPayload = {
          name: cleanName,
          description: cleanDescription,
          orgtypeidentifier: formValue.orgtypeidentifier
        };
        this.confirm.emit(createPayload);
      }

      this.form.reset();
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleCancel(): void {
    this.form.reset();
    this.close.emit();
  }
}
