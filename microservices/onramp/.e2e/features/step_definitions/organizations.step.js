const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');

Given('I am on the organizations page', async function () {
  const baseUrl = process.env.BASE_URL || 'http://localhost:8080';
  const targetUrl = `${baseUrl}/organizations`;
  try {
    await this.driver.get(targetUrl);
    if (typeof this.waitForAngularStable === 'function') {
      await this.waitForAngularStable();
    }
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 30000);
    await this.driver.wait(until.elementLocated(By.id('title-organizations')), 30000);
  } catch (err) {
    console.error('Navigation or element location error:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see the page title {string}', async function (expectedTitle) {
  try {
    const titleElement = await this.driver.findElement(By.id('title-organizations'));
    const actualTitle = await titleElement.getText();
    if (actualTitle !== expectedTitle) {
      throw new Error(`Expected title "${expectedTitle}" but got "${actualTitle}"`);
    }
  } catch (err) {
    console.error('Title verification error:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see the organization list with at least one record', async function () {
  try {
    const orgRows = await this.driver.findElements(By.css('#organization-list .default-row, #organization-list .highlight-row'));
    if (orgRows.length === 0) {
      throw new Error('No organizations found in the list');
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('the first organization\'s name is not empty', async function () {
  try {
    const firstOrgName = await this.driver.findElement(By.css('#organization-list .default-row:first-child a, #organization-list .highlight-test-row:first-child a')).getText();
    if (!firstOrgName.trim()) {
      throw new Error('First organization\'s name is empty');
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I enter the search keyword {string}', async function (searchTerm) {
  try {
    const searchInput = await this.driver.findElement(By.css('.form-search input[nz-input]'));
    await searchInput.clear();
    await searchInput.sendKeys(searchTerm);
    await this.driver.sleep(1000);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see the organization list only contains organizations named {string}', async function (expectedName) {
  try {
    const orgNames = await this.driver.findElements(By.css('#organization-list .default-row a, #organization-list .highlight-row a'));
    for (let name of orgNames) {
      const text = await name.getText();
      if (text !== expectedName) {
        throw new Error(`Found unexpected organization name "${text}", expected "${expectedName}"`);
      }
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I click the add organization button', async function () {
  try {
    const addButton = await this.driver.findElement(By.css('.add-btn'));
    await addButton.click();
    const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
    let isVisible = await modal.isDisplayed();
    let attempts = 0;
    while (!isVisible && attempts < 3) {
      await this.driver.sleep(1000);
      isVisible = await modal.isDisplayed();
      attempts++;
    }
    if (!isVisible) {
      throw new Error('Modal is not visible after waiting');
    }
  } catch (err) {
    console.error('Error in add button click:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I fill in the organization name {string}', async function (orgName) {
  try {
    const nameInput = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-name"]')), 10000);
    await nameInput.clear();
    await nameInput.sendKeys(orgName);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I submit the new organization form', async function () {
  try {
    const submitButton = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')), 10000);
    await submitButton.click();
    await this.driver.sleep(3000);
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I see {string} in the organization list', async function (orgName) {
  try {
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 30000);
    const orgLinks = await this.driver.findElements(By.css('#organization-list .default-row a, #organization-list .highlight-row a'));
    let found = false;
    const allNames = [];
    for (let link of orgLinks) {
      const text = await link.getText();
      const trimmedText = text.trim();
      allNames.push(trimmedText);
      if (trimmedText === orgName) {
        found = true;
        break;
      }
    }
    if (!found) {
      const listContent = await this.driver.findElement(By.id('organization-list')).getAttribute('innerHTML');
      await this.checkTheDatabaseFor(orgName);
      throw new Error(`Expected to see "${orgName}" but found names: ${allNames.join(', ')}`);
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I click the edit button of the first organization', async function () {
  try {
    const editButton = await this.driver.findElement(By.css('#organization-list .default-row:first-child .edit-btn, #organization-list .highlight-row:first-child .edit-btn'));
    await editButton.click();
    const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
    let isVisible = await modal.isDisplayed();
    let attempts = 0;
    while (!isVisible && attempts < 3) {
      await this.driver.sleep(1000);
      isVisible = await modal.isDisplayed();
      attempts++;
    }
    if (!isVisible) {
      throw new Error('Modal is not visible after waiting');
    }
  } catch (err) {
    console.error('Error in edit button click:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I change the organization name to {string}', async function (newName) {
  try {
    const nameInput = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-name"]')), 10000);
    await nameInput.clear();
    await nameInput.sendKeys(newName);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I submit the edit form', async function () {
  try {
    const submitButton = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')), 15000);
    await submitButton.click();
    await this.driver.sleep(5000);
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 25000);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I add a new organization named {string}', async function (orgName) {
  try {
    await this.clickTheAddOrganizationButton();
    await this.fillInTheOrganizationName(orgName);
    await this.submitTheNewOrganizationForm();
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I check the database for {string}', async function (orgName) {
  try {
    const result = await this.checkTheDatabaseFor(orgName);
    const fullResponse = await this.driver.executeAsyncScript(`
      var callback = arguments[arguments.length - 1];
      fetch('${process.env.BASE_URL || 'http://localhost:8080'}/api/organizations', { method: 'GET' })
        .then(response => response.json())
        .then(data => callback(data))
        .catch(err => callback(null));
    `);
  } catch (err) {
    console.error('Error in check the database for:', err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('the database contains {string}', async function (orgName) {
  try {
    if (typeof this.lastDbCheck === 'undefined' || this.lastDbCheck === null) {
      throw new Error('Database check failed or not performed');
    }
    if (!this.lastDbCheck) {
      throw new Error(`Database does not contain "${orgName}"`);
    }
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I refresh the organization list', async function () {
  try {
    await this.driver.navigate().refresh();
    await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
  } catch (err) {
    console.error(err);
    console.error('Page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});