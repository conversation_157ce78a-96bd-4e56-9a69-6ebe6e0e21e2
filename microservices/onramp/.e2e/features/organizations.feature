Feature: Organizations page
  As a user,
  I want to view, search, add, and edit organizations,
  So that I can manage organization data effectively and ensure changes persist.

  Scenario: Display the organizations page title
    Given I am on the organizations page
    Then I see the page title "Organizations"
  
  Scenario: View the organization list
    Given I am on the organizations page
    Then I see the organization list with at least one record
    And the first organization's name is not empty
  
  Sc<PERSON><PERSON>: Search the organization list
    Given I am on the organizations page
    When I enter the search keyword "Persist Test Org"
    Then I see the organization list only contains organizations named "Persist Test Org"
  
  Scenario: Add a new organization
    Given I am on the organizations page
    When I click the add organization button
    And I fill in the organization name "New Test Organization"
    And I submit the new organization form
    Then I see "New Test Organization" in the organization list
    
  Sc<PERSON>rio: Edit an existing organization
    Given I am on the organizations page
    When I click the edit button of the first organization
    And I change the organization name to "Updated Test Org"
    And I submit the edit form
    Then I see "Updated Test Org" in the organization list

  Scenario: Validate data persistence to database
    Given I am on the organizations page
    When I add a new organization named "Persist Test Org"
    And I check the database for "Persist Test Org"
    Then the database contains "Persist Test Org"
