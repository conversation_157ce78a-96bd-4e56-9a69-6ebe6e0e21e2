package rmsEngine

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

func TestHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name         string
		connectorErr error
		receiveErr   error
		attrErr      error
		unmarshalErr error
		messages     []*gatewayv1.DeviceEntry
		batchErr     error
		processErr   bool
		batchAddErr  error
		marshalErr   error
		dlqErr       error
		upsertErr    error
		wantDLQ      int
		wantAdds     int
	}{
		{"Connector error", errors.New("no conn"), nil, nil, nil, nil, nil, false, nil, nil, nil, nil, 0, 0},
		{"Receive error", nil, errors.New("recv fail"), nil, nil, nil, nil, false, nil, nil, nil, nil, 0, 0},
		{"JSON unmarshal error", nil, nil, errors.New("fail attr parse"), nil, nil, nil, false, nil, nil, nil, nil, 1, 0},
		{"Unmarshal error", nil, nil, nil, errors.New("bad proto"), nil, nil, false, nil, nil, nil, nil, 1, 0},
		{"GetBatch error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("x")}}, errors.New("no batch"), false, nil, nil, nil, nil, 0, 0},
		{"ProcessRMSEngine error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d2", Message: []byte("m")}}, nil, true, nil, nil, nil, nil, 1, 0},
		{"Batch Add error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d3", Message: []byte("y")}}, nil, false, errors.New("add fail"), nil, nil, nil, 1, 1},
		{"Marshal error path", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "ee", Message: []byte("msg")}}, nil, true, nil, errors.New("marshal fail"), nil, nil, 0, 0},
		{"DLQ error on JSON unmarshal", nil, nil, errors.New("fail attr parse"), nil, nil, nil, false, nil, nil, errors.New("dlq fail"), nil, 1, 0},
		{"DLQ error on Unmarshal error", nil, nil, nil, errors.New("bad proto"), nil, nil, false, nil, nil, errors.New("dlq fail"), nil, 1, 0},
		{"DLQ error on ProcessRMSEngine error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d2", Message: []byte("m")}}, nil, true, nil, nil, errors.New("dlq fail"), nil, 1, 0},
		{"Upsert error", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d4", Message: []byte("z")}}, nil, false, nil, nil, nil, errors.New("asdasf"), 0, 1},
		{"Happy path", nil, nil, nil, nil, []*gatewayv1.DeviceEntry{{DeviceId: "d4", Message: []byte("z")}}, nil, false, nil, nil, nil, nil, 0, 1},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			ctx := context.Background()
			dlqCount := 0
			addCount := 0

			// Setup fake connections and Pub/Sub client
			conn := mocks.FakeConns()
			client := conn.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr
			fdb := &mocks.FakeDBExecutor{}
			conn.Postgres = fdb

			// Setup FakeBatcher
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				addCount++
				return tc.batchAddErr
			}

			// Build HandlerDeps
			deps := HandlerDeps{
				Connector: ConnectorFunc(func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conn, tc.connectorErr
				}),
				SendToDLQ: DLQSender(func(ctx context.Context, c connect.PsClient, msg *pubsub.Message, reason string) error {
					dlqCount++
					return tc.dlqErr
				}),
				UnmarshalDevice: UnmarshalDeviceDataFunc(func(raw []byte) (*gatewayv1.DeviceData, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					return &gatewayv1.DeviceData{Messages: tc.messages}, nil
				}),
				GetBatch: BatchGetter(func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.batchErr != nil {
						return nil, tc.batchErr
					}
					return fakeBatch, nil
				}),
				ProcessRMSEngine: ProcessRMSEngineFunc(func(_ *pubsubdata.HeaderDetails, raw []byte) (*edihelper.RmsEngineRecord, *edihelper.HeaderRecord, error) {
					if tc.processErr {
						return nil, nil, errors.New("proc fail")
					}
					return &edihelper.RmsEngineRecord{}, &edihelper.HeaderRecord{}, nil
				}),
				ToBQ: ToBQConverter(func(orgID, sgwID, tz, topic, pubsubID, deviceID string, ts time.Time, header schemas.HeaderRecord, rawMsg []byte, engineData *edihelper.RmsEngineRecord) schemas.RmsEngine {
					return schemas.RmsEngine{}
				}),
				MarshalDevice: MarshalDeviceDataFunc(func(msg proto.Message) ([]byte, error) {
					if tc.marshalErr != nil {
						return nil, tc.marshalErr
					}
					return []byte{1, 2, 3}, nil
				}),
				ParseAttributes: ParseAttributesFunc(func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				}),
				UpsertDevice: func(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceRmsEngine) error {
					if tc.upsertErr != nil {
						return tc.upsertErr
					}
					return upsertDeviceRMSEngine(pg, rec)
				},
			}

			// Publish test message if no connector or receive error
			if tc.connectorErr == nil && tc.receiveErr == nil {
				msg := &pubsub.Message{Data: []byte{1, 2, 3}, ID: "id", PublishTime: time.Now()}

				topic := client.Topic("topic")
				topic.Publish(ctx, msg)
				client.CreateSubscription(ctx, "sub", connect.SubscriptionConfig{Topic: topic})
			}

			// Invoke handler
			h := HandlerWithDeps(deps)
			h(ctx, "sub")

			// Verify counts
			assert.Equal(t, tc.wantDLQ, dlqCount, "DLQ calls")
			assert.Equal(t, tc.wantAdds, addCount, "Batch adds")
		})
	}
}

type MockResult struct{}

func (m MockResult) LastInsertId() (int64, error) { return 0, nil }
func (m MockResult) RowsAffected() (int64, error) { return 1, nil }

func TestUpsertDeviceRMSEngine(t *testing.T) {
	now := time.Now()
	records := map[string]*edihelper.DeviceRmsEngine{
		"device-1": {
			EngineVersion:   edihelper.ConvertByteToDecimalFormat(1),
			EngineRevision:  edihelper.ConvertByteToDecimalFormat(1),
			PubsubTimestamp: now.Add(-5 * time.Minute),
			UpdatedAt:       now,
		},
		"device-2": {
			EngineVersion:   edihelper.ConvertByteToDecimalFormat(1),
			EngineRevision:  edihelper.ConvertByteToDecimalFormat(1),
			PubsubTimestamp: now.Add(-2 * time.Minute),
			UpdatedAt:       now,
		},
	}
	fdb := &mocks.FakeDBExecutor{}

	err := upsertDeviceRMSEngine(fdb, records)
	assert.NoError(t, err)
}

func TestUpsertDeviceRMSEngine_emptyRecords(t *testing.T) {
	fdb := &mocks.FakeDBExecutor{}
	rec := map[string]*edihelper.DeviceRmsEngine{}
	err := upsertDeviceRMSEngine(fdb, rec)
	assert.NoError(t, err, "expected no error when input map is empty")
}
