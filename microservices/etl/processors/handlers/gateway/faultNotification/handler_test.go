package faultNotification

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	"synapse-its.com/shared/bqbatch"
	connect "synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

var originalOsGetenv = osGetenv

// --- defaultUpsertDeviceFault table tests ---

func TestDefaultUpsertDeviceFault(t *testing.T) {
	t.Parallel()

	now := time.Now().UTC()
	rec := &edihelper.RmsStatusRecord{
		IsFaulted:           true,
		Fault:               "No Error",
		FaultStatus:         "OK",
		ChannelGreenStatus:  []bool{true, false},
		ChannelYellowStatus: []bool{false, true},
		ChannelRedStatus:    []bool{false, false},
		MonitorTime:         now,
		Temperature:         42,
		VoltagesGreen:       []int64{1, 1},
		VoltagesYellow:      []int64{2, 2},
		VoltagesRed:         []int64{3, 3},
		DeviceModel:         "EDIMMU16LE",
	}

	cases := []struct {
		name       string
		enableFail bool
		failAfter  int
		wantErr    bool
	}{
		{"success", false, 0, false},
		{"db error", true, 0, true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Setup fake database executor
			fdb := &mocks.FakeDBExecutor{}
			if tc.enableFail {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = tc.failAfter
			}

			// Execute function under test
			err := upsertDeviceFault(fdb, "550e8400-e29b-41d4-a716-446655440000", rec)

			// Assert results
			if tc.wantErr {
				assert.Error(t, err, "expected error but got nil")
				assert.Error(t, err, "should return wrapped upsert error")
			} else {
				assert.NoError(t, err, "unexpected error")
				assert.Equal(t, 1, fdb.ExecCallCount, "should execute query exactly once")
			}
		})
	}
}

// --- HandlerWithDeps table tests ---

func TestHandlerWithDeps(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	now := time.Now()

	cases := []struct {
		name         string
		connErr      error
		recvErr      error
		attrErr      error
		unmarshalErr error
		batchErr     error
		processErr   bool
		batchAddErr  error
		upsertErr    bool
		marshalErr   error
		dlqErr       error

		wantDLQ       int
		wantExecCalls int
		wantBatchAdds int
	}{
		{
			name:    "connector error",
			connErr: errors.New("no conn"),
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:    "receive error",
			recvErr: errors.New("recv fail"),
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:    "attribute parse error",
			attrErr: errors.New("fail attr parse"),
			wantDLQ: 1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal error",
			unmarshalErr: errors.New("bad proto"),
			wantDLQ:      1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "get batch error",
			unmarshalErr: nil,
			batchErr:     errors.New("no batch"),
			// on GetBatch error we Nack but do not DLQ
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:       "process error",
			processErr: true,
			wantDLQ:    1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:        "batch add error",
			batchAddErr: errors.New("add fail"),
			wantDLQ:     1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:      "upsert error",
			upsertErr: true,
			wantDLQ:   1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:        "marshal error",
			batchAddErr: errors.New("any"),
			marshalErr:  errors.New("marshal fail"),
			wantDLQ:     0, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:    "JSON parse + DLQ error", // TODO: check for nack
			attrErr: errors.New("fail attr parse"),
			dlqErr:  errors.New("dlq failed"),
			wantDLQ: 1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "proto unmarshal + DLQ error", // TODO: check for nack
			unmarshalErr: errors.New("bad proto"),
			dlqErr:       errors.New("dlq failed"),
			wantDLQ:      1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:        "final DLQ error path", // TODO: check for ack
			batchAddErr: errors.New("any"),      // forces unprocessed path
			dlqErr:      errors.New("dlq failed"),
			wantDLQ:     1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:    "happy path",
			wantDLQ: 0, wantExecCalls: 1, wantBatchAdds: 1,
		},
	}

	for _, tc := range cases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			// --- setup fakes ---
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// create subscription
			topic := psc.Topic("topic-" + tc.name)
			_, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				msg := &pubsub.Message{ID: "m-" + tc.name, Data: []byte{1, 2, 3}, PublishTime: now}
				topic.Publish(ctx, msg)
			}

			// swap in FakeDBExecutor and configure for upsert error
			fdb := &mocks.FakeDBExecutor{}
			if tc.upsertErr {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = 0
			}
			conns.Postgres = fdb

			// fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				added++
				return nil
			}

			// collect DLQ calls
			dlq := 0

			// --- build deps ---
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					dlq++
					return tc.dlqErr
				},
				UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					// default single‐message
					return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "dev1", Message: []byte("p")},
					}}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
					if tc.processErr {
						return nil, nil, errors.New("proc fail")
					}
					return &edihelper.RmsStatusRecord{
							ChannelGreenStatus:  []bool{true},
							ChannelYellowStatus: []bool{false},
							ChannelRedStatus:    []bool{true},
							MonitorTime:         time.Now().UTC(),
							Temperature:         0,
							VoltagesGreen:       []int64{},
							VoltagesYellow:      []int64{},
							VoltagesRed:         []int64{},
							DeviceModel:         "M",
						},
						&edihelper.HeaderRecord{}, nil
				},
				ToBQ:          edihelper.RmsStatusToFaultNotification,
				UpsertDevice:  upsertDeviceFault,
				MarshalDevice: func(msg proto.Message) ([]byte, error) { return []byte{1, 2, 3}, tc.marshalErr },
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				},
				// Add missing dependencies that were causing the panic
				CreateNotificationService: func(connections *connect.Connections) NotificationService {
					return &MockNotificationService{
						ProcessFaultNotificationFunc: func(ctx context.Context, faultData *FaultNotificationData) error {
							return nil
						},
					}
				},
				GetWebsiteAppsURL: func() string {
					return "https://test.synapse-its.app/apps/"
				},
			}

			// --- invoke handler ---
			h := HandlerWithDeps(deps)
			h(ctx, "sub-"+tc.name)

			// --- asserts ---
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ calls should match expected")
			assert.Equal(t, tc.wantExecCalls, fdb.ExecCallCount, "ExecCallCount should match expected")
			assert.Equal(t, tc.wantBatchAdds, added, "batch.Add calls should match expected")
		})
	}
}

// Test_createNotificationService tests the createNotificationService function
func Test_createNotificationService(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupConnsFn   func() *connect.Connections
		expectedType   string
		shouldNotPanic bool
	}{
		{
			name: "success_with_valid_connections",
			setupConnsFn: func() *connect.Connections {
				conns := mocks.FakeConns()
				return conns
			},
			expectedType:   "*faultNotification.notificationService",
			shouldNotPanic: true,
		},
		{
			name: "success_with_nil_postgres",
			setupConnsFn: func() *connect.Connections {
				conns := mocks.FakeConns()
				conns.Postgres = nil
				return conns
			},
			expectedType:   "*faultNotification.notificationService",
			shouldNotPanic: true,
		},
		{
			name: "success_with_nil_pubsub",
			setupConnsFn: func() *connect.Connections {
				conns := mocks.FakeConns()
				conns.Pubsub = nil
				return conns
			},
			expectedType:   "*faultNotification.notificationService",
			shouldNotPanic: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup connections
			conns := tt.setupConnsFn()

			// Execute function under test
			var result NotificationService
			var testPanic bool

			func() {
				defer func() {
					if r := recover(); r != nil {
						testPanic = true
					}
				}()
				result = createNotificationService(conns)
			}()

			// Assert results
			if tt.shouldNotPanic {
				assert.False(t, testPanic, "function should not panic")
				assert.NotNil(t, result, "should return non-nil NotificationService")
				assert.Implements(t, (*NotificationService)(nil), result, "should implement NotificationService interface")
			} else {
				assert.True(t, testPanic, "function should panic")
			}
		})
	}
}

// Test_getWebsiteAppsURL tests the getWebsiteAppsURL function
func Test_getWebsiteAppsURL(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		envValue    string
		expectedURL string
		setupFn     func(string)
		restoreFn   func()
	}{
		{
			name:        "success_with_env_var_set_with_slash",
			envValue:    "https://example.com/apps/",
			expectedURL: "https://example.com/apps/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_env_var_set_without_slash",
			envValue:    "https://example.com/apps",
			expectedURL: "https://example.com/apps/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_empty_env_var",
			envValue:    "",
			expectedURL: "https://www.synapse-its.app/apps/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_custom_url",
			envValue:    "https://custom.domain.com/path",
			expectedURL: "https://custom.domain.com/path/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
		{
			name:        "success_with_single_character_url",
			envValue:    "/",
			expectedURL: "/",
			setupFn: func(value string) {
				osGetenv = func(key string) string {
					if key == "WEBSITE_APPS" {
						return value
					}
					return ""
				}
			},
			restoreFn: func() {
				osGetenv = originalOsGetenv
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup environment mock
			tt.setupFn(tt.envValue)
			defer tt.restoreFn()

			// Execute function under test
			result := getWebsiteAppsURL()

			// Assert results
			assert.Equal(t, tt.expectedURL, result, "should return expected URL")
			assert.True(t, len(result) > 0, "URL should not be empty")
			assert.True(t, result[len(result)-1] == '/', "URL should end with slash")
		})
	}
}

// Test_processSingleFaultNotificationWithSemaphore tests the processSingleFaultNotificationWithSemaphore function
func Test_processSingleFaultNotificationWithSemaphore(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                     string
		faultData                FaultNotificationData
		setupNotificationService func() NotificationService
		semaphoreBufferSize      int
		expectedProcessCalls     int
		expectedError            bool
		concurrentCalls          int
	}{
		{
			name: "success_single_notification",
			faultData: FaultNotificationData{
				DeviceID:       "device-123",
				UserDeviceID:   "device-123",
				UserDeviceName: "Test Device",
				FaultReason:    "High Temperature",
				FaultedAt:      time.Now().UTC(),
			},
			setupNotificationService: func() NotificationService {
				mockService := &MockNotificationService{}
				mockService.ProcessFaultNotificationFunc = func(ctx context.Context, faultData *FaultNotificationData) error {
					return nil
				}
				return mockService
			},
			semaphoreBufferSize:  1,
			expectedProcessCalls: 1,
			expectedError:        false,
			concurrentCalls:      1,
		},
		{
			name: "success_notification_service_error",
			faultData: FaultNotificationData{
				DeviceID:       "device-456",
				UserDeviceID:   "device-456",
				UserDeviceName: "Test Device 2",
				FaultReason:    "Low Voltage",
				FaultedAt:      time.Now().UTC(),
			},
			setupNotificationService: func() NotificationService {
				mockService := &MockNotificationService{}
				mockService.ProcessFaultNotificationFunc = func(ctx context.Context, faultData *FaultNotificationData) error {
					return errors.New("notification service error")
				}
				return mockService
			},
			semaphoreBufferSize:  1,
			expectedProcessCalls: 1,
			expectedError:        true,
			concurrentCalls:      1,
		},
		{
			name: "success_concurrent_processing",
			faultData: FaultNotificationData{
				DeviceID:       "device-789",
				UserDeviceID:   "device-789",
				UserDeviceName: "Test Device 3",
				FaultReason:    "Connection Lost",
				FaultedAt:      time.Now().UTC(),
			},
			setupNotificationService: func() NotificationService {
				mockService := &MockNotificationService{}
				mockService.ProcessFaultNotificationFunc = func(ctx context.Context, faultData *FaultNotificationData) error {
					// Simulate some processing time
					time.Sleep(10 * time.Millisecond)
					return nil
				}
				return mockService
			},
			semaphoreBufferSize:  3,
			expectedProcessCalls: 3,
			expectedError:        false,
			concurrentCalls:      3,
		},
		{
			name: "success_semaphore_limiting",
			faultData: FaultNotificationData{
				DeviceID:       "device-limit",
				UserDeviceID:   "device-limit",
				UserDeviceName: "Limited Device",
				FaultReason:    "Rate Limited",
				FaultedAt:      time.Now().UTC(),
			},
			setupNotificationService: func() NotificationService {
				mockService := &MockNotificationService{}
				mockService.ProcessFaultNotificationFunc = func(ctx context.Context, faultData *FaultNotificationData) error {
					// Simulate some processing time to test semaphore
					time.Sleep(50 * time.Millisecond)
					return nil
				}
				return mockService
			},
			semaphoreBufferSize:  1, // Limited buffer
			expectedProcessCalls: 2,
			expectedError:        false,
			concurrentCalls:      2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup notification service
			notificationService := tt.setupNotificationService()
			mockService := notificationService.(*MockNotificationService)

			// Create semaphore
			semaphore := make(chan struct{}, tt.semaphoreBufferSize)

			// Execute function under test (potentially multiple times for concurrency)
			ctx := context.Background()
			var wg sync.WaitGroup

			for i := 0; i < tt.concurrentCalls; i++ {
				wg.Add(1)
				// Acquire semaphore before starting goroutine (matching production code pattern)
				semaphore <- struct{}{}
				go processSingleFaultNotificationWithSemaphore(ctx, notificationService, semaphore, tt.faultData, &wg)
			}

			// Wait for all goroutines to complete with timeout
			done := make(chan struct{})
			go func() {
				wg.Wait()
				close(done)
			}()

			select {
			case <-done:
				// All goroutines completed
			case <-time.After(2 * time.Second):
				t.Fatal("Test timed out waiting for goroutines to complete")
			}

			// Assert results
			assert.Equal(t, tt.expectedProcessCalls, mockService.ProcessFaultNotificationCallCount, "should call ProcessFaultNotification expected number of times")

			// Verify semaphore is properly released (should be empty)
			assert.Equal(t, 0, len(semaphore), "semaphore should be empty after processing")
		})
	}
}

// Test_processSingleFaultNotificationWithSemaphore_semaphore_blocking tests semaphore behavior
func Test_processSingleFaultNotificationWithSemaphore_semaphore_blocking(t *testing.T) {
	t.Parallel()

	t.Run("semaphore_blocks_when_full", func(t *testing.T) {
		t.Parallel()

		// Setup notification service with slow processing
		mockService := &MockNotificationService{}
		processDelay := 100 * time.Millisecond
		mockService.ProcessFaultNotificationFunc = func(ctx context.Context, faultData *FaultNotificationData) error {
			time.Sleep(processDelay)
			return nil
		}

		// Create semaphore with buffer size 1
		semaphore := make(chan struct{}, 1)

		faultData := FaultNotificationData{
			DeviceID:       "device-block-test",
			UserDeviceID:   "device-block-test",
			UserDeviceName: "Block Test Device",
			FaultReason:    "Semaphore Test",
			FaultedAt:      time.Now().UTC(),
		}

		ctx := context.Background()
		var wg sync.WaitGroup

		start := time.Now()

		// Start two goroutines - second should block until first completes
		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore for first goroutine
		go processSingleFaultNotificationWithSemaphore(ctx, mockService, semaphore, faultData, &wg)

		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore for second goroutine (will block)
		go processSingleFaultNotificationWithSemaphore(ctx, mockService, semaphore, faultData, &wg)

		// Wait for both to complete
		wg.Wait()

		elapsed := time.Since(start)

		// Should take at least 2 * processDelay since they run sequentially
		minExpected := 2 * processDelay
		assert.True(t, elapsed >= minExpected, "processing should take at least %v but took %v", minExpected, elapsed)
		assert.Equal(t, 2, mockService.ProcessFaultNotificationCallCount, "should process both notifications")
	})
}

// MockNotificationService for testing
type MockNotificationService struct {
	ProcessFaultNotificationFunc      func(ctx context.Context, faultData *FaultNotificationData) error
	ProcessFaultNotificationCallCount int
}

func (m *MockNotificationService) ProcessFaultNotification(ctx context.Context, faultData *FaultNotificationData) error {
	m.ProcessFaultNotificationCallCount++
	if m.ProcessFaultNotificationFunc != nil {
		return m.ProcessFaultNotificationFunc(ctx, faultData)
	}
	return nil
}

// Test_handlerWithDeps_missing_dependencies tests the missing dependencies that caused the panic
func Test_handlerWithDeps_missing_dependencies(t *testing.T) {
	t.Parallel()

	t.Run("fix_missing_dependencies_in_HandlerDeps", func(t *testing.T) {
		t.Parallel()

		// This test demonstrates the fix for the panic in the existing TestHandlerWithDeps
		ctx := context.Background()
		conns := mocks.FakeConns()

		// Create proper HandlerDeps with all required functions
		deps := HandlerDeps{
			Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
				return conns, nil
			},
			ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
				return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
			},
			SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
				return nil
			},
			UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
				return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
					{DeviceId: "dev1", Message: []byte("test")},
				}}, nil
			},
			GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
				fakeBatch, _ := mocks.FakeBatch(ctx)
				return fakeBatch, nil
			},
			ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
				return &edihelper.RmsStatusRecord{
					IsFaulted:           true, // Make it faulted to test notification path
					ChannelGreenStatus:  []bool{true},
					ChannelYellowStatus: []bool{false},
					ChannelRedStatus:    []bool{true},
					MonitorTime:         time.Now().UTC(),
					Temperature:         0,
					VoltagesGreen:       []int64{},
					VoltagesYellow:      []int64{},
					VoltagesRed:         []int64{},
					DeviceModel:         "TestModel",
					Fault:               "Test Fault",
				}, &edihelper.HeaderRecord{}, nil
			},
			ToBQ:         edihelper.RmsStatusToFaultNotification,
			UpsertDevice: upsertDeviceFault,
			MarshalDevice: func(msg proto.Message) ([]byte, error) {
				return []byte{1, 2, 3}, nil
			},
			// These were missing and causing the panic:
			CreateNotificationService: func(connections *connect.Connections) NotificationService {
				return &MockNotificationService{
					ProcessFaultNotificationFunc: func(ctx context.Context, faultData *FaultNotificationData) error {
						return nil
					},
				}
			},
			GetWebsiteAppsURL: func() string {
				return "https://test.example.com/apps/"
			},
		}

		// Create and setup subscription
		psc := conns.Pubsub.(*mocks.FakePubsubClient)
		topic := psc.Topic("test-topic")
		_, err := psc.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})
		assert.NoError(t, err, "should create subscription without error")

		// Publish a test message
		msg := &pubsub.Message{
			ID:          "test-msg",
			Data:        []byte{1, 2, 3},
			PublishTime: time.Now(),
		}
		topic.Publish(ctx, msg)

		// Execute function under test
		handler := HandlerWithDeps(deps)

		// This should not panic now
		assert.NotPanics(t, func() {
			handler(ctx, "test-sub")
		}, "handler should not panic with proper dependencies")
	})
}

// Test_handlerWithDeps_fault_notification_path tests the fault notification processing path
func Test_handlerWithDeps_fault_notification_path(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                      string
		isFaulted                 bool
		expectedNotificationCalls int
	}{
		{
			name:                      "faulted_device_triggers_notification",
			isFaulted:                 true,
			expectedNotificationCalls: 1,
		},
		{
			name:                      "non_faulted_device_skips_notification",
			isFaulted:                 false,
			expectedNotificationCalls: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			conns := mocks.FakeConns()

			// Mock notification service to track calls
			mockNotificationService := &MockNotificationService{
				ProcessFaultNotificationFunc: func(ctx context.Context, faultData *FaultNotificationData) error {
					return nil
				},
			}

			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, nil
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					return nil
				},
				UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
					return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "test-device", Message: []byte("test")},
					}}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					fakeBatch, _ := mocks.FakeBatch(ctx)
					return fakeBatch, nil
				},
				ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
					return &edihelper.RmsStatusRecord{
						IsFaulted:           tt.isFaulted,
						ChannelGreenStatus:  []bool{true},
						ChannelYellowStatus: []bool{false},
						ChannelRedStatus:    []bool{true},
						MonitorTime:         time.Now().UTC(),
						Temperature:         25,
						VoltagesGreen:       []int64{12},
						VoltagesYellow:      []int64{12},
						VoltagesRed:         []int64{12},
						DeviceModel:         "TestModel",
						Fault:               "Test Fault Reason",
					}, &edihelper.HeaderRecord{}, nil
				},
				ToBQ:         edihelper.RmsStatusToFaultNotification,
				UpsertDevice: upsertDeviceFault,
				MarshalDevice: func(msg proto.Message) ([]byte, error) {
					return []byte{1, 2, 3}, nil
				},
				CreateNotificationService: func(connections *connect.Connections) NotificationService {
					return mockNotificationService
				},
				GetWebsiteAppsURL: func() string {
					return "https://test.example.com/apps/"
				},
			}

			// Setup subscription and message
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			topic := psc.Topic("fault-test-topic")
			_, err := psc.CreateSubscription(ctx, "fault-test-sub", connect.SubscriptionConfig{Topic: topic})
			assert.NoError(t, err, "should create subscription without error")

			msg := &pubsub.Message{
				ID:          "fault-test-msg",
				Data:        []byte{1, 2, 3},
				PublishTime: time.Now(),
			}
			topic.Publish(ctx, msg)

			// Execute handler
			handler := HandlerWithDeps(deps)
			handler(ctx, "fault-test-sub")

			// Give time for goroutines to complete
			time.Sleep(100 * time.Millisecond)

			// Assert notification calls
			assert.Equal(t, tt.expectedNotificationCalls, mockNotificationService.ProcessFaultNotificationCallCount, "should call ProcessFaultNotification expected number of times")
		})
	}
}
