package monitorName

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	mocks "synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// Test error definitions for consistent error handling
var (
	ErrTestConnector  = errors.New("no conn")
	ErrTestReceive    = errors.New("recv fail")
	ErrTestUnmarshal  = errors.New("bad proto")
	ErrTestBatch      = errors.New("no batch")
	ErrTestProcessRMS = errors.New("proc fail")
	ErrTestBatchAdd   = errors.New("add fail")
	ErrTestMarshal    = errors.New("marshal fail")
	ErrTestDLQ        = errors.New("dlq fail")
	ErrUpsert         = errors.New("upsert fail")
)

// Test_HandlerWithDeps_scenarios tests the HandlerWithDeps function with various error scenarios
func Test_HandlerWithDeps_scenarios(t *testing.T) {
	// Valid device message with minimum required bytes for header parsing
	validDeviceMessage := []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07}

	tests := []struct {
		name         string                   // Required: descriptive test name
		connectorErr error                    // Error from Connector function
		receiveErr   error                    // Error from Subscription.Receive
		attrErr      error                    // Error from attribute parsing
		unmarshalErr error                    // Error from UnmarshalDevice function
		messages     []*gatewayv1.DeviceEntry // Device messages to process
		batchErr     error                    // Error from GetBatch function
		processErr   bool                     // Whether ProcessMonitor should return error
		batchAddErr  error                    // Error from batch.Add function
		marshalErr   error                    // Error from MarshalBase64 function
		dlqErr       error                    // Error from SendToDLQ function
		upsertErr    error

		// Expected results
		wantDLQ  int // Expected number of DLQ calls
		wantAdds int // Expected number of batch add calls
	}{
		{
			name:         "connector_error",
			connectorErr: ErrTestConnector,
			wantDLQ:      0,
			wantAdds:     0,
		},
		{
			name:       "receive_error",
			receiveErr: ErrTestReceive,
			wantDLQ:    0,
			wantAdds:   0,
		},
		{
			name:     "json_unmarshal_error",
			attrErr:  errors.New("fail attr parse"),
			wantDLQ:  1,
			wantAdds: 0,
		},
		{
			name:     "json_unmarshal_with_dlq_error",
			attrErr:  errors.New("fail attr parse"),
			dlqErr:   ErrTestDLQ,
			wantDLQ:  1,
			wantAdds: 0,
		},
		{
			name:         "device_unmarshal_error",
			unmarshalErr: ErrTestUnmarshal,
			wantDLQ:      1,
			wantAdds:     0,
		},
		{
			name:         "device_unmarshal_with_dlq_error",
			unmarshalErr: ErrTestUnmarshal,
			dlqErr:       ErrTestDLQ,
			wantDLQ:      1,
			wantAdds:     0,
		},
		{
			name:     "batch_get_error",
			messages: []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: validDeviceMessage}},
			batchErr: ErrTestBatch,
			wantDLQ:  0,
			wantAdds: 0,
		},
		{
			name:       "process_monitor_error",
			messages:   []*gatewayv1.DeviceEntry{{DeviceId: "d2", Message: validDeviceMessage}},
			processErr: true,
			wantDLQ:    1,
			wantAdds:   0,
		},
		{
			name:        "batch_add_error",
			messages:    []*gatewayv1.DeviceEntry{{DeviceId: "d3", Message: validDeviceMessage}},
			batchAddErr: ErrTestBatchAdd,
			wantDLQ:     1,
			wantAdds:    1,
		},
		{
			name:       "marshal_error_with_unprocessed",
			messages:   []*gatewayv1.DeviceEntry{{DeviceId: "ee", Message: validDeviceMessage}},
			processErr: true,
			marshalErr: ErrTestMarshal,
			wantDLQ:    0,
			wantAdds:   0,
		},
		{
			name:       "json_marshal_with_dlq_error",
			messages:   []*gatewayv1.DeviceEntry{{DeviceId: "ee", Message: validDeviceMessage}},
			processErr: true,
			dlqErr:     ErrTestDLQ,
			wantDLQ:    1,
			wantAdds:   0,
		},
		{
			name:     "happy_path_success",
			messages: []*gatewayv1.DeviceEntry{{DeviceId: "d4", Message: validDeviceMessage}},
			wantDLQ:  0,
			wantAdds: 1,
		},
		{
			name:      "upsert_error",
			messages:  []*gatewayv1.DeviceEntry{{DeviceId: "d4", Message: validDeviceMessage}},
			upsertErr: ErrUpsert,
			wantDLQ:   0,
			wantAdds:  1,
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution for independent tests
			t.Parallel()

			// Initialize test context and counters
			ctx := context.Background()
			dlqCount := 0
			addCount := 0

			// Setup fake connections and Pub/Sub client for testing
			conn := mocks.FakeConns()
			client := conn.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			// Setup FakeBatcher with custom Add function to track calls
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				addCount++
				return tc.batchAddErr
			}

			// fdb := &mocks.FakeDBExecutor{}
			fdb := &mocks.FakeDBExecutor{}
			conn.Postgres = fdb

			// Build HandlerDeps with mocked dependencies
			deps := HandlerDeps{
				Connector: ConnectorFunc(func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conn, tc.connectorErr
				}),
				SendToDLQ: DLQSender(func(ctx context.Context, c connect.PsClient, msg *pubsub.Message, reason string) error {
					dlqCount++
					return tc.dlqErr
				}),
				UnmarshalDevice: UnmarshalDeviceDataFunc(func(raw []byte) (*gatewayv1.DeviceData, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					return &gatewayv1.DeviceData{Messages: tc.messages}, nil
				}),
				GetBatch: BatchGetter(func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.batchErr != nil {
						return nil, tc.batchErr
					}
					return fakeBatch, nil
				}),
				ProcessMonitor: ProcessMonitorFunc(func(_ *pubsubdata.HeaderDetails, raw []byte) (monitorDetail *edihelper.MonitorNameAndId, headerDetails *edihelper.HeaderRecord, err error) {
					if tc.processErr {
						return nil, nil, ErrTestProcessRMS
					}
					return &edihelper.MonitorNameAndId{}, &edihelper.HeaderRecord{}, nil
				}),
				ToBQ: ToBQConverter(func(orgID, sgwID, tz, topic, pubsubID, deviceID string, ts time.Time, header schemas.HeaderRecord, rawMsg []byte, status *edihelper.MonitorNameAndId) schemas.MonitorName {
					return schemas.MonitorName{}
				}),
				MarshalDevice: MarshalDeviceDataFunc(func(msg proto.Message) ([]byte, error) {
					if tc.marshalErr != nil {
						return nil, tc.marshalErr
					}
					return []byte{1, 2, 3}, nil
				}),
				ParseAttributes: ParseAttributesFunc(func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				}),
				UpsertDevice: func(pg connect.DatabaseExecutor, rec map[string]*edihelper.DeviceMonitorName) error {
					if tc.upsertErr != nil {
						return tc.upsertErr
					}
					return upsertDeviceMonitorName(pg, rec)
				},
			}

			// Publish test message if no connector or receive error expected
			if tc.connectorErr == nil && tc.receiveErr == nil {
				msg := &pubsub.Message{Data: []byte{1, 2, 3}, ID: "id", PublishTime: time.Now()}

				// Setup topic and subscription for message processing
				topic := client.Topic("topic")
				topic.Publish(ctx, msg)
				client.CreateSubscription(ctx, "sub", connect.SubscriptionConfig{Topic: topic})
			}

			// Execute the handler under test
			h := HandlerWithDeps(deps)
			h(ctx, "sub")

			// Assert expected results using testify assert package
			assert.Equal(t, tc.wantDLQ, dlqCount, "DLQ calls should match expected count")
			assert.Equal(t, tc.wantAdds, addCount, "Batch add calls should match expected count")
		})
	}
}

type MockResult struct{}

func (m MockResult) LastInsertId() (int64, error) { return 0, nil }
func (m MockResult) RowsAffected() (int64, error) { return 1, nil }

func TestUpsertDeviceMonitorName(t *testing.T) {
	now := time.Now()
	records := map[string]*edihelper.DeviceMonitorName{
		"device-1": {
			MonitorID:       1,
			MonitorName:     "M-1",
			PubsubTimestamp: now.Add(-5 * time.Minute),
			UpdatedAt:       now,
		},
		"device-2": {
			MonitorID:       2,
			MonitorName:     "M-2",
			PubsubTimestamp: now.Add(-2 * time.Minute),
			UpdatedAt:       now,
		},
	}

	fdb := &mocks.FakeDBExecutor{}
	err := upsertDeviceMonitorName(fdb, records)
	assert.NoError(t, err)
}

func TestUpsertDeviceMonitorName_emptyRecords(t *testing.T) {
	fdb := &mocks.FakeDBExecutor{}
	rec := map[string]*edihelper.DeviceMonitorName{}
	err := upsertDeviceMonitorName(fdb, rec)
	assert.NoError(t, err, "expected no error when input map is empty")
}
