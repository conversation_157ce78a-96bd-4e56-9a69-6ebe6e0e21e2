package purgeExpired

import (
	"context"
	"testing"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/firestore"
	fakeps "synapse-its.com/shared/mocks/pubsub"
)

type mockConnector struct {
	mock.Mock
}

func (m *mockConnector) GetConnections(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	args := m.Called(ctx, checkConnections)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*connect.Connections), args.Error(1)
}

type mockService struct {
	mock.Mock
}

func (m *mockService) PurgeExpiredData(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func TestCreateHandler(t *testing.T) {
	tests := []struct {
		name             string
		subscriptionName string
		mockSetup        func(*mockConnector, *fakeps.FakePubsubClient, *mockService)
		expectError      bool
	}{
		{
			name:             "successful message processing",
			subscriptionName: "test-sub",
			mockSetup: func(mockConn *mockConnector, fakePS *fakeps.FakePubsubClient, mockSvc *mockService) {
				// Create topic and subscription
				topic := fakePS.Topic("test-topic")
				fakePS.CreateSubscription(context.Background(), "test-sub", connect.SubscriptionConfig{Topic: topic})

				// Publish a test message
				topic.Publish(context.Background(), &pubsub.Message{
					Data: []byte("test-message"),
				})

				// Setup service expectations
				mockSvc.On("PurgeExpiredData", mock.Anything).Return(nil)

				connections := &connect.Connections{
					Pubsub:    fakePS,
					Postgres:  &dbexecutor.FakeDBExecutor{},
					Firestore: firestore.NewFakeFirestoreClient(),
				}
				mockConn.On("GetConnections", mock.Anything, mock.Anything).Return(connections, nil)
			},
			expectError: false,
		},
		{
			name:             "error getting connections",
			subscriptionName: "test-sub",
			mockSetup: func(mockConn *mockConnector, fakePS *fakeps.FakePubsubClient, mockSvc *mockService) {
				mockConn.On("GetConnections", mock.Anything, mock.Anything).Return(nil, assert.AnError)
			},
			expectError: true,
		},
		{
			name:             "error purging expired data",
			subscriptionName: "test-sub",
			mockSetup: func(mockConn *mockConnector, fakePS *fakeps.FakePubsubClient, mockSvc *mockService) {
				// Create topic and subscription
				topic := fakePS.Topic("test-topic")
				fakePS.CreateSubscription(context.Background(), "test-sub", connect.SubscriptionConfig{Topic: topic})

				// Publish a test message
				topic.Publish(context.Background(), &pubsub.Message{
					Data: []byte("test-message"),
				})

				// Setup service expectations
				mockSvc.On("PurgeExpiredData", mock.Anything).Return(assert.AnError)

				connections := &connect.Connections{
					Pubsub:    fakePS,
					Postgres:  &dbexecutor.FakeDBExecutor{},
					Firestore: firestore.NewFakeFirestoreClient(),
				}
				mockConn.On("GetConnections", mock.Anything, mock.Anything).Return(connections, nil)
			},
			expectError: true,
		},
		{
			name:             "error receiving messages from subscription",
			subscriptionName: "test-sub",
			mockSetup: func(mockConn *mockConnector, fakePS *fakeps.FakePubsubClient, mockSvc *mockService) {
				// Set up the fake PubSub client to return an error on Receive
				fakePS.ReceiveError = assert.AnError

				connections := &connect.Connections{
					Pubsub:    fakePS,
					Postgres:  &dbexecutor.FakeDBExecutor{},
					Firestore: firestore.NewFakeFirestoreClient(),
				}
				mockConn.On("GetConnections", mock.Anything, mock.Anything).Return(connections, nil)
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockConn := new(mockConnector)
			fakePS := fakeps.NewFakePubsubClient()
			mockSvc := new(mockService)
			tt.mockSetup(mockConn, fakePS, mockSvc)

			handler := createHandler(handlerDeps{
				connector: mockConn.GetConnections,
				createService: func(connections *connect.Connections) Service {
					return mockSvc
				},
			})

			// Execute handler
			handler(context.Background(), tt.subscriptionName)

			// Verify mock expectations
			mockConn.AssertExpectations(t)
			mockSvc.AssertExpectations(t)

			// Verify topic and subscription if successful
			if !tt.expectError {
				topic := fakePS.Topic("test-topic").(*fakeps.FakePubsubTopic)
				messages := topic.GetMessages()
				assert.NotNil(t, messages)
				assert.Len(t, messages, 1)
			}
		})
	}
}

func TestCreateService(t *testing.T) {
	connections := &connect.Connections{
		Postgres:  &dbexecutor.FakeDBExecutor{},
		Firestore: firestore.NewFakeFirestoreClient(),
	}
	service := createService(connections)
	assert.NotNil(t, service)
}
