package dlqBqBatch

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func TestHandlerWithDeps(t *testing.T) {
	type testCase struct {
		name             string
		getBatchErr      error
		connectorErr     error
		receiveErr       error
		messages         []*pubsub.Message
		batcherAddErr    error
		batcherLoadErr   error
		expectAddCount   int
		expectLoadCount  int
		expectRetryCount int
	}

	tests := []testCase{
		{
			name:        "GetBatchError",
			getBatchErr: errors.New("batch error"),
			// Connector should not be invoked
			expectAddCount:  0,
			expectLoadCount: 0,
		},
		{
			name:         "ConnectorError",
			getBatchErr:  nil,
			connectorErr: errors.New("connector error"),
			// No messages processed
			expectAddCount:  0,
			expectLoadCount: 0,
		},
		{
			name:            "ReceiveError",
			getBatchErr:     nil,
			connectorErr:    nil,
			receiveErr:      errors.New("receive error"),
			messages:        nil, // subscription.Receive returns error first
			expectAddCount:  0,
			expectLoadCount: 0,
		},
		{
			name:         "MalformedJSON_AddSuccess",
			getBatchErr:  nil,
			connectorErr: nil,
			receiveErr:   nil,
			messages: []*pubsub.Message{
				{
					Data:        []byte("not a valid json"),
					ID:          "msg1",
					PublishTime: time.Now(),
				},
			},
			batcherAddErr:   nil,
			expectAddCount:  1,
			expectLoadCount: 0,
		},
		{
			name:         "MalformedJSON_AddError",
			getBatchErr:  nil,
			connectorErr: nil,
			receiveErr:   nil,
			messages: []*pubsub.Message{
				{
					Data:        []byte("invalid_json"),
					ID:          "msg2",
					PublishTime: time.Now(),
				},
			},
			batcherAddErr:   errors.New("add error"),
			expectAddCount:  1,
			expectLoadCount: 0,
		},
		{
			name:         "ValidJSON_LoadSuccess",
			getBatchErr:  nil,
			connectorErr: nil,
			receiveErr:   nil,
			messages: func() []*pubsub.Message {
				failed := bqbatch.FailedBatch{
					Table:      "table1",
					Rows:       []map[string]interface{}{{}, {}},
					RetryCount: 0,
				}
				data, _ := json.Marshal(failed)
				return []*pubsub.Message{
					{
						Data:        data,
						ID:          "msg3",
						PublishTime: time.Now(),
					},
				}
			}(),
			batcherLoadErr:   nil,
			expectAddCount:   0,
			expectLoadCount:  1,
			expectRetryCount: 0,
		},
		{
			name:         "ValidJSON_LoadError_AddSuccess",
			getBatchErr:  nil,
			connectorErr: nil,
			receiveErr:   nil,
			messages: func() []*pubsub.Message {
				failed := bqbatch.FailedBatch{
					Table:      "table2",
					Rows:       []map[string]interface{}{{}, {}, {}},
					RetryCount: 0,
				}
				data, _ := json.Marshal(failed)
				return []*pubsub.Message{
					{
						Data:        data,
						ID:          "msg4",
						PublishTime: time.Now(),
					},
				}
			}(),
			batcherLoadErr:   errors.New("load error"),
			batcherAddErr:    nil,
			expectAddCount:   1,
			expectLoadCount:  1,
			expectRetryCount: 0,
		},
		{
			name:         "ValidJSON_LoadError_AddError",
			getBatchErr:  nil,
			connectorErr: nil,
			receiveErr:   nil,
			messages: func() []*pubsub.Message {
				failed := bqbatch.FailedBatch{
					Table:      "table3",
					Rows:       []map[string]interface{}{{}, {}, {}},
					RetryCount: 0,
				}
				data, _ := json.Marshal(failed)
				return []*pubsub.Message{
					{
						Data:        data,
						ID:          "msg5",
						PublishTime: time.Now(),
					},
				}
			}(),
			batcherLoadErr:   errors.New("load error"),
			batcherAddErr:    errors.New("add error"),
			expectAddCount:   1,
			expectLoadCount:  1,
			expectRetryCount: 0,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			subName := "test-sub-" + tc.name

			// Track whether Connector was invoked (for GetBatchError/ConnectorError)
			connectorCalled := false

			// Prepare FakeBatcher
			var addCount, loadCount int
			var lastProcessed bqbatch.FailedBatch

			fb := &mocks.FakeBatcher{
				AddFn: func(row interface{}) error {
					addCount++
					return tc.batcherAddErr
				},
				LoadBatchFn: func(failed bqbatch.FailedBatch) error {
					loadCount++
					lastProcessed = failed
					return tc.batcherLoadErr
				},
			}

			// Build deps
			deps := HandlerDeps{
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.getBatchErr != nil {
						return nil, tc.getBatchErr
					}
					return fb, nil
				},
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					connectorCalled = true
					if tc.connectorErr != nil {
						return nil, tc.connectorErr
					}
					// Build fake connections and set up pubsub
					conns := mocks.FakeConns()
					client := conns.Pubsub.(*mocks.FakePubsubClient)
					client.ReceiveError = tc.receiveErr

					if len(tc.messages) > 0 {
						// Create topic and subscription
						topic := client.Topic(subName)
						// Publish each test message to topic
						for _, m := range tc.messages {
							topic.Publish(ctx, m)
						}
						// Bind subscription to that topic
						client.CreateSubscription(ctx, subName, connect.SubscriptionConfig{Topic: topic})
					}
					return conns, nil
				},
				ParseAttributes: nil, // not used in current handler
			}

			handler := HandlerWithDeps(deps)
			handler(ctx, subName)

			// Assertions
			// If getBatchErr was set, Connector should never be called
			if tc.getBatchErr != nil {
				assert.False(t, connectorCalled, "Connector should not be called when GetBatch fails")
			} else {
				assert.True(t, connectorCalled, "Connector should be called when GetBatch succeeds")
			}

			// Check counts
			assert.Equal(t, tc.expectAddCount, addCount, "unexpected Add call count")
			assert.Equal(t, tc.expectLoadCount, loadCount, "unexpected LoadBatch call count")

			// If expecting a retry, verify RetryCount increment
			if tc.expectRetryCount >= 0 && loadCount > 0 {
				assert.Equal(t, tc.expectRetryCount, lastProcessed.RetryCount, "unexpected RetryCount on processed batch")
			}
		})
	}
}
