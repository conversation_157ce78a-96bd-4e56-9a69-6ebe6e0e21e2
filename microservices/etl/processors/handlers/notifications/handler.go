package notifications

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/etl/processors/handlers/notifications/twilio"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// Incoming notification messages
type NotificationMessage struct {
	Type     string                 `json:"type"`
	Payload  map[string]interface{} `json:"payload"`
	Metadata map[string]interface{} `json:"metadata"`
}

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	Connector       func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributes func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	SendToDLQ       func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	jsonUnmarshal   func(data []byte, v any) error
	jsonMarshal     func(v any) ([]byte, error)
	GetBatch        func(ctx context.Context) (bqbatch.Batcher, error)
	NewService      func() NotificationService
}

// Implement subscription handler
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
	return func(ctx context.Context, subscriptionName string) {
		// Get batch for BQ insert
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}

		// Get connections
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}

		// Create notification service
		notificationService := deps.NewService()

		// Set up subscription
		sub := connections.Pubsub.Subscription(subscriptionName)
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s\n", subscriptionName, string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, _, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Parse notification message
			var notif NotificationMessage
			if err := deps.jsonUnmarshal(msg.Data, &notif); err != nil {
				logger.Errorf("Failed to parse notification message: %v", err)
				// Send to DLQ for invalid JSON
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to parse notification: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to DLQ: %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Add to BigQuery batch
			// Convert payload and metadata to bigquery.NullJSON
			payloadJSON, err := deps.jsonMarshal(notif.Payload)
			if err != nil {
				logger.Errorf("Failed to marshal payload: %v", err)
				// Send to DLQ for marshaling errors (permanent failure)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to marshal payload: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to DLQ: %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}
			metadataJSON, err := deps.jsonMarshal(notif.Metadata)
			if err != nil {
				logger.Errorf("Failed to marshal metadata: %v", err)
				// Send to DLQ for marshaling errors (permanent failure)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to marshal metadata: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to DLQ: %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Determine if payload and metadata are valid (non-empty)
			payloadValid := isValidJSON(notif.Payload)
			metadataValid := isValidJSON(notif.Metadata)

			bqItem := schemas.NotificationMessages{
				OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
				Topic:                  commonAttrs.Topic,
				PubsubTimestamp:        msg.PublishTime.UTC(),
				PubsubID:               msg.ID,
				NotificationType:       notif.Type,
				Payload:                bigquery.NullJSON{JSONVal: string(payloadJSON), Valid: payloadValid},
				Metadata:               bigquery.NullJSON{JSONVal: string(metadataJSON), Valid: metadataValid},
				RawMessage:             msg.Data,
			}
			if err = batch.Add(bqItem); err != nil {
				logger.Errorf("Error adding message to batch: %v", err)
				msg.Nack()
				return
			}

			// Process based on notification type
			switch notif.Type {
			case "sms":
				// Extract SMS-specific fields
				to, ok := notif.Payload["to"].(string)
				if !ok {
					logger.Errorf("Missing 'to' field in SMS notification")
					msg.Ack() // Invalid payload, no retry
					return
				}

				message, ok := notif.Payload["message"].(string)
				if !ok {
					logger.Errorf("Missing 'message' field in SMS notification")
					msg.Ack() // Invalid payload, no retry
					return
				}

				// Send SMS
				err := notificationService.SendSMS(ctx, to, message)
				if err != nil {
					logger.Errorf("Failed to send SMS: %v", err)
					// Determine if error is transient
					if isTransientError(err) {
						msg.Nack()
						return
					}
					// Permanent error, send to DLQ
					err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to send SMS: %v", err))
					if err != nil {
						logger.Errorf("Error sending message to DLQ: %v", err)
						msg.Nack()
						return
					}
					msg.Ack()
					return
				}

				logger.Infof("Successfully sent SMS to %s", to)
				msg.Ack()

			default:
				logger.Errorf("Unsupported notification type: %s", notif.Type)
				msg.Ack() // Unknown type
			}
		})
		if err != nil {
			logger.Errorf("Error receiving messages: %v", err)
		}
	}
}

// isValidJSON checks if a map contains meaningful data (not nil or empty)
func isValidJSON(data map[string]interface{}) bool {
	return data != nil && len(data) > 0
}

// isTransientError checks if an error is transient
func isTransientError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	// Network-related errors that might be transient
	transientPatterns := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"network is unreachable",
		"temporary failure",
		"service unavailable",
		"rate limit",
		"too many requests",
		"internal server error",
		"bad gateway",
		"gateway timeout",
	}

	for _, pattern := range transientPatterns {
		if strings.Contains(errStr, pattern) {
			return true
		}
	}

	return false
}

var notificationService = func() NotificationService {
	return twilio.NewService()
}

// Handler is the production-ready Pub/Sub processor using real dependencies
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	GetBatch:        bqbatch.GetBatch,
	jsonUnmarshal:   json.Unmarshal,
	jsonMarshal:     json.Marshal,
	NewService:      notificationService,
})
