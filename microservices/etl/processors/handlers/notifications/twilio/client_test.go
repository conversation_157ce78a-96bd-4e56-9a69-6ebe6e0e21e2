package twilio

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/twilio/twilio-go"
)

func TestNewClient(t *testing.T) {
	// Save original osGetenv and restore after test
	origGetenv := osGetenv
	defer func() { osGetenv = origGetenv }()

	tests := []struct {
		name      string
		envVars   map[string]string
		wantError error
	}{
		{
			name: "success",
			envVars: map[string]string{
				"TWILIO_ACCOUNT_SID": "test-sid",
				"TWILIO_API_KEY":     "test-key",
				"TWILIO_API_SECRET":  "test-secret",
			},
			wantError: nil,
		},
		{
			name: "missing_account_sid",
			envVars: map[string]string{
				"TWILIO_API_KEY":    "test-key",
				"TWILIO_API_SECRET": "test-secret",
			},
			wantError: ErrMissingAccountSid,
		},
		{
			name: "missing_api_key",
			envVars: map[string]string{
				"TWILIO_ACCOUNT_SID": "test-sid",
				"TWILIO_API_SECRET":  "test-secret",
			},
			wantError: ErrMissingAPIKey,
		},
		{
			name: "missing_api_secret",
			envVars: map[string]string{
				"TWILIO_ACCOUNT_SID": "test-sid",
				"TWILIO_API_KEY":     "test-key",
			},
			wantError: ErrMissingAPISecret,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Mock environment variables
			osGetenv = func(key string) string {
				return tc.envVars[key]
			}

			// Call NewClient
			client, err := NewClient(context.Background())

			// Assert error
			if tc.wantError != nil {
				assert.Error(t, err)
				assert.Equal(t, tc.wantError, err)
				assert.Nil(t, client)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, client)
			}
		})
	}
}

func TestWithClient(t *testing.T) {
	// Create a test client
	client := &twilio.RestClient{}

	// Create a context with the client
	ctx := context.Background()
	ctxWithClient := WithClient(ctx, client)

	// Verify client is stored in context
	storedClient := ctxWithClient.Value(twilioClientKey)
	assert.NotNil(t, storedClient)
	assert.Equal(t, client, storedClient)
}

func TestFromContext(t *testing.T) {
	tests := []struct {
		name     string
		setupCtx func() context.Context
		wantNil  bool
	}{
		{
			name: "client_exists",
			setupCtx: func() context.Context {
				return WithClient(context.Background(), &twilio.RestClient{})
			},
			wantNil: false,
		},
		{
			name: "no_client",
			setupCtx: func() context.Context {
				return context.Background()
			},
			wantNil: true,
		},
		{
			name: "wrong_type",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), twilioClientKey, "not a client")
			},
			wantNil: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctx := tc.setupCtx()
			client := FromContext(ctx)
			if tc.wantNil {
				assert.Nil(t, client)
			} else {
				assert.NotNil(t, client)
			}
		})
	}
}

func Test_getCustomHTTPClient(t *testing.T) {
	t.Setenv("TWILIO_STUB", "")
	t.Setenv("TWILIO_HOST", "")

	t.Run("default client", func(t *testing.T) {
		client := getCustomHTTPClient()
		assert.Equal(t, http.DefaultClient, client)
	})

	t.Run("stub client", func(t *testing.T) {
		t.Setenv("TWILIO_STUB", "1")
		t.Setenv("TWILIO_HOST", "")

		client := getCustomHTTPClient()
		resp, err := client.Get("http://example.com")
		assert.NoError(t, err)
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		assert.NoError(t, err)
		assert.Equal(t, `{"sid":"SM_STUBBED","status":"sent"}`, string(body))
	})

	t.Run("redirect client", func(t *testing.T) {
		t.Setenv("TWILIO_STUB", "")
		t.Setenv("TWILIO_HOST", "127.0.0.1:9999") // won't be used, overridden below

		called := false
		mock := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			w.WriteHeader(204)
		}))
		defer mock.Close()

		overrideHost := strings.TrimPrefix(mock.URL, "http://")
		client := newRedirectClient(overrideHost)

		resp, err := client.Get("http://twilio.com/test")
		assert.NoError(t, err)
		assert.Equal(t, 204, resp.StatusCode)
		resp.Body.Close()

		assert.True(t, called, "expected redirect client to call mock server")
	})

	t.Run("redirect client", func(t *testing.T) {
		t.Setenv("TWILIO_STUB", "")
		mock := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(204)
		}))
		defer mock.Close()

		overrideHost := strings.TrimPrefix(mock.URL, "http://")
		t.Setenv("TWILIO_HOST", overrideHost)

		client := getCustomHTTPClient()

		resp, err := client.Get("http://twilio.com/test")
		assert.NoError(t, err)
		assert.Equal(t, 204, resp.StatusCode)
		resp.Body.Close()
	})
}

func Test_newStubClient(t *testing.T) {
	client := newStubClient(202, "stubbed response")
	resp, err := client.Get("http://any.url")
	assert.NoError(t, err)
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	assert.Equal(t, "stubbed response", string(body))
	assert.Equal(t, 202, resp.StatusCode)
}

func Test_newRedirectClient(t *testing.T) {
	called := false
	mock := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		called = true
		w.WriteHeader(204)
	}))
	defer mock.Close()

	host := strings.TrimPrefix(mock.URL, "http://")
	client := newRedirectClient(host)

	resp, err := client.Get("http://example.com")
	assert.NoError(t, err)
	assert.Equal(t, 204, resp.StatusCode)
	resp.Body.Close()

	assert.True(t, called, "expected request to be redirected to mock server")
}

func Test_roundTripperFunc(t *testing.T) {
	called := false
	rt := roundTripperFunc(func(req *http.Request) (*http.Response, error) {
		called = true
		return &http.Response{
			StatusCode: 204,
			Body:       io.NopCloser(strings.NewReader("")),
			Header:     make(http.Header),
			Request:    req,
		}, nil
	})

	client := &http.Client{Transport: rt}
	resp, err := client.Get("http://example.com")
	assert.NoError(t, err)
	defer resp.Body.Close()

	assert.True(t, called, "expected roundTripperFunc to be called")
	assert.Equal(t, 204, resp.StatusCode)
}
