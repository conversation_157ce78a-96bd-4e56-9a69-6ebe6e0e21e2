package notifications

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	mocknotif "synapse-its.com/shared/mocks/notifications"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// createTestMessage creates a test notification message
func createTestMessage(t *testing.T, notifType string, payload map[string]interface{}, metadata map[string]interface{}) *pubsub.Message {
	notif := NotificationMessage{
		Type:     notifType,
		Payload:  payload,
		Metadata: metadata,
	}

	data, err := json.Marshal(notif)
	if err != nil {
		t.Fatal(err)
	}

	return &pubsub.Message{
		ID:   "test-msg-id",
		Data: data,
		Attributes: map[string]string{
			"organizationIdentifier": "test-org",
			"topic":                  "test-topic",
		},
		PublishTime: time.Now(),
	}
}

func TestHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name               string
		connErr            error
		recvErr            error
		attrErr            error
		batchErr           error
		batchAddErr        error
		dlqErr             error
		smsErr             error
		jsonErr            error
		payloadMarshalErr  error
		metadataMarshalErr error
		notifType          string
		payload            map[string]interface{}
		metadata           map[string]interface{}
		wantDLQ            int
		wantBatchAdds      int
		wantSMSSent        bool
	}{
		{
			name:          "get batch error",
			batchErr:      errors.New("no batch"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "connector error",
			connErr:       errors.New("no conn"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "receive error",
			recvErr:       errors.New("recv fail"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "parse attributes error",
			attrErr:       errors.New("fail attr parse"),
			notifType:     "sms",
			wantDLQ:       1,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "parse attributes error and fail to send to DLQ",
			attrErr:       errors.New("fail attr parse"),
			dlqErr:        errors.New("dlq failed"),
			notifType:     "sms",
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "invalid JSON message",
			notifType:     "sms",
			payload:       nil,
			wantDLQ:       1,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "JSON unmarshal error",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			jsonErr:       errors.New("json unmarshal failed"),
			wantDLQ:       1,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "JSON unmarshal error and fail to send to DLQ",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			jsonErr:       errors.New("json unmarshal failed"),
			dlqErr:        errors.New("dlq failed"),
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:              "payload marshal error",
			notifType:         "sms",
			payload:           map[string]interface{}{"to": "1234567890", "message": "test"},
			payloadMarshalErr: errors.New("payload marshal failed"),
			wantDLQ:           1,
			wantBatchAdds:     0,
			wantSMSSent:       false,
		},
		{
			name:              "payload marshal error and fail to send to DLQ",
			notifType:         "sms",
			payload:           map[string]interface{}{"to": "1234567890", "message": "test"},
			payloadMarshalErr: errors.New("payload marshal failed"),
			dlqErr:            errors.New("dlq failed"),
			wantDLQ:           0,
			wantBatchAdds:     0,
			wantSMSSent:       false,
		},
		{
			name:               "metadata marshal error",
			notifType:          "sms",
			payload:            map[string]interface{}{"to": "1234567890", "message": "test"},
			metadata:           map[string]interface{}{"source": "test"},
			metadataMarshalErr: errors.New("metadata marshal failed"),
			wantDLQ:            1,
			wantBatchAdds:      0,
			wantSMSSent:        false,
		},
		{
			name:               "metadata marshal error and fail to send to DLQ",
			notifType:          "sms",
			payload:            map[string]interface{}{"to": "1234567890", "message": "test"},
			metadata:           map[string]interface{}{"source": "test"},
			metadataMarshalErr: errors.New("metadata marshal failed"),
			dlqErr:             errors.New("dlq failed"),
			wantDLQ:            0,
			wantBatchAdds:      0,
			wantSMSSent:        false,
		},
		{
			name:          "batch add error",
			batchAddErr:   errors.New("batch add failed"),
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			wantDLQ:       0,
			wantBatchAdds: 0,
			wantSMSSent:   false,
		},
		{
			name:          "unsupported notification type",
			notifType:     "email",
			payload:       map[string]interface{}{"to": "<EMAIL>", "message": "test"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
		},
		{
			name:          "missing to field in SMS",
			notifType:     "sms",
			payload:       map[string]interface{}{"message": "test"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
		},
		{
			name:          "missing message field in SMS",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
		},
		{
			name:          "SMS send error - transient",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			smsErr:        errors.New("connection timeout"),
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
		},
		{
			name:          "SMS send error - permanent",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			smsErr:        errors.New("invalid phone number"),
			wantDLQ:       1,
			wantBatchAdds: 1,
			wantSMSSent:   false,
		},
		{
			name:          "SMS send error - permanent and DLQ fails",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			smsErr:        errors.New("invalid phone number"),
			dlqErr:        errors.New("dlq failed"),
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   false,
		},
		{
			name:          "happy path - SMS sent successfully",
			notifType:     "sms",
			payload:       map[string]interface{}{"to": "1234567890", "message": "test"},
			metadata:      map[string]interface{}{"source": "test"},
			wantDLQ:       0,
			wantBatchAdds: 1,
			wantSMSSent:   true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()

			// Setup mocks
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// Create subscription
			topic := psc.Topic("topic-" + tc.name)
			_, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// Publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				var msg *pubsub.Message
				if tc.name == "invalid JSON message" {
					// Create invalid JSON message
					msg = &pubsub.Message{
						ID:   "test-msg-id",
						Data: []byte(`{"type": "sms", "payload": {invalid json}`),
						Attributes: map[string]string{
							"organizationIdentifier": "test-org",
							"topic":                  "test-topic",
						},
						PublishTime: time.Now(),
					}
				} else {
					msg = createTestMessage(t, tc.notifType, tc.payload, tc.metadata)
				}
				topic.Publish(ctx, msg)
			}

			// Setup fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				// Verify the row is of correct type
				_, ok := row.(schemas.NotificationMessages)
				assert.True(t, ok, "Row should be of type schemas.NotificationMessages")
				added++
				return nil
			}

			// Setup mock notification service
			mockService := &mocknotif.MockNotificationService{}
			if tc.payload != nil && tc.payload["to"] != nil && tc.payload["message"] != nil {
				mockService.On("SendSMS", mock.Anything, tc.payload["to"].(string), tc.payload["message"].(string)).Return(tc.smsErr)
			}

			// Count DLQ calls
			dlq := 0

			// Build deps
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					if tc.dlqErr != nil {
						return tc.dlqErr
					}
					dlq++
					return nil
				},
				jsonUnmarshal: func(data []byte, v any) error {
					if tc.jsonErr != nil {
						return tc.jsonErr
					}
					return json.Unmarshal(data, v)
				},
				jsonMarshal: func(v interface{}) ([]byte, error) {
					// Check if we should simulate marshaling errors
					if tc.payloadMarshalErr != nil {
						// Look at the payload content to identify if this is the payload marshal call
						if payloadMap, ok := v.(map[string]interface{}); ok {
							if tc.payload != nil && len(payloadMap) == len(tc.payload) {
								// This looks like our payload
								return nil, tc.payloadMarshalErr
							}
						}
					}
					if tc.metadataMarshalErr != nil {
						// Look at the metadata content to identify if this is the metadata marshal call
						if metadataMap, ok := v.(map[string]interface{}); ok {
							if tc.metadata != nil && len(metadataMap) == len(tc.metadata) {
								// This looks like our metadata
								return nil, tc.metadataMarshalErr
							}
						}
					}
					return json.Marshal(v)
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{
						OrganizationIdentifier: "test-org",
						Topic:                  "test-topic",
					}, pubsubdata.HeaderDetails{}, nil
				},
				NewService: func() NotificationService {
					return mockService
				},
			}

			// Run handler
			handler := HandlerWithDeps(deps)
			handler(ctx, "sub-"+tc.name)

			// Verify expectations
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ count mismatch")
			assert.Equal(t, tc.wantBatchAdds, added, "Batch adds count mismatch")

			if tc.wantSMSSent && tc.payload != nil && tc.payload["to"] != nil && tc.payload["message"] != nil {
				mockService.AssertCalled(t, "SendSMS", mock.Anything, tc.payload["to"].(string), tc.payload["message"].(string))
			}
		})
	}
}

func Test_isTransientError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "timeout error",
			err:      errors.New("connection timeout"),
			expected: true,
		},
		{
			name:     "connection refused",
			err:      errors.New("connection refused"),
			expected: true,
		},
		{
			name:     "rate limit",
			err:      errors.New("rate limit exceeded"),
			expected: true,
		},
		{
			name:     "connection reset",
			err:      errors.New("connection reset by peer"),
			expected: true,
		},
		{
			name:     "network unreachable",
			err:      errors.New("network is unreachable"),
			expected: true,
		},
		{
			name:     "temporary failure",
			err:      errors.New("temporary failure in name resolution"),
			expected: true,
		},
		{
			name:     "service unavailable",
			err:      errors.New("service unavailable"),
			expected: true,
		},
		{
			name:     "too many requests",
			err:      errors.New("too many requests"),
			expected: true,
		},
		{
			name:     "internal server error",
			err:      errors.New("internal server error"),
			expected: true,
		},
		{
			name:     "bad gateway",
			err:      errors.New("bad gateway"),
			expected: true,
		},
		{
			name:     "gateway timeout",
			err:      errors.New("gateway timeout"),
			expected: true,
		},
		{
			name:     "permanent error",
			err:      errors.New("invalid phone number"),
			expected: false,
		},
		{
			name:     "another permanent error",
			err:      errors.New("authentication failed"),
			expected: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			result := isTransientError(tc.err)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func Test_notificationService(t *testing.T) {
	tests := []struct {
		name     string
		expected bool
	}{
		{
			name:     "returns valid NotificationService",
			expected: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Call the notificationService function
			service := notificationService()

			// Verify service is not nil
			assert.NotNil(t, service, "notificationService should return a non-nil service")

			// Verify it implements NotificationService interface
			var _ NotificationService = service

			// Verify it's the expected type (Twilio service)
			// We can't directly check the type since it might be an interface,
			// but we can verify it has the expected behavior
			ctx := context.Background()

			// Test that SendSMS method exists and can be called
			// (even if it fails due to missing credentials, the method should exist)
			err := service.SendSMS(ctx, "test", "test")
			// We don't check the error since we don't have real credentials
			// We just verify the method can be called without panic
			_ = err
		})
	}
}

func Test_Handler(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "Handler variable is initialized",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Verify Handler is not nil
			assert.NotNil(t, Handler, "Handler should be initialized")

			// Verify Handler is a function
			assert.IsType(t, func(context.Context, string) {}, Handler, "Handler should be a function")
		})
	}
}
