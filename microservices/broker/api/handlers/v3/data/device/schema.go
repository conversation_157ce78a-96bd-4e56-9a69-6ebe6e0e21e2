package device

import (
	"time"
)

type pgDeviceInfo struct {
	MonitorTime               time.Time
	Fault                     string
	ChannelGreenStatus        []bool
	ChannelYellowStatus       []bool
	ChannelRedStatus          []bool
	ID                        int // device Id
	DeviceID                  string
	Latitude                  string
	Longitude                 string
	IPAddress                 string
	Port                      string
	MonitorID                 int    // user set id
	MonitorName               string // user set name
	EngineVersion             string
	EngineRevision            string
	DateUploadedUTC           time.Time
	SoftwareGatewayIdentifier string
}

// Data payload included in HTTP response
type dataPayload struct {
	DeviceID         int            `json:"device_id"`
	DeviceIdentifier string         `json:"device_identifier"`
	Location         location       `json:"location"`
	Status           deviceStatus   `json:"status"`
	Metadata         deviceMetadata `json:"device_info"`
}

type location struct {
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
}

type deviceMetadata struct {
	Manufacturer            string `json:"manufacturer"`
	Model                   string `json:"model"`
	UserAssignedDeviceID    string `json:"user_assigned_device_id"`
	UserAssignedDeviceName  string `json:"user_assigned_device_name"`
	ApplicationVersion      string `json:"application_version"`
	FirmwareType            string `json:"firmware_type"`
	FirmwareVersion         string `json:"firmware_version"`
	CommVersion             string `json:"comm_version"`
	RmsEngineFirmwareType   string `json:"rms_engine_firmware_type"`
	RmsEngineFirmwareVerson string `json:"rms_engine_firmware_version"`
	IPAddress               string `json:"ip_address"`
	IPort                   string `json:"port"`
}

type deviceStatus struct {
	State                string        `json:"state"`
	HeartbeatReceivedUTC string        `json:"heartbeat_received_utc"`
	LogUploadedUTC       string        `json:"log_uploaded_utc"`
	LastFaultReason      string        `json:"last_fault_reason"`
	LastFaultUploadedUTC string        `json:"last_fault_uploaded_utc"`
	FaultedChannelStatus channelStatus `json:"faulted_channel_status"`
}

type channelStatus struct {
	ChannelRed    []bool `json:"red"`
	ChannelYellow []bool `json:"yellow"`
	ChannelGreen  []bool `json:"green"`
}
