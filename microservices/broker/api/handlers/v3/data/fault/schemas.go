package fault

import "time"

// UnifiedLogData holds every field defined in any Logs_<Device>.proto.
type Logs_16leip_UnifiedLogData struct {
	MessageTime            time.Time                          `json:"message_time"`
	DeviceID               string                             `json:"device_id"`
	Schema                 string                             `json:"schema"`
	DeviceDetail           Logs_16leip_DeviceDetail           `json:"device_detail"`
	LogMonitorReset        Logs_16leip_LogMonitorReset        `json:"log_monitor_reset"`
	LogPreviousFail        Logs_16leip_LogPreviousFail        `json:"log_previous_fail"`
	LogConfigurationChange Logs_16leip_LogConfigurationChange `json:"log_configuration"`
	LogAcLineEvent         Logs_16leip_LogAcLineEvent         `json:"log_ac_line_event"`
	LogFaultSignalSequence Logs_16leip_LogFaultSignalSequence `json:"log_fault_signal_sequence"`
}

// ─── DeviceDetail ────────────────────────────────────────────────────────────

type Logs_16leip_DeviceDetail struct {
	DeviceID                 string `json:"device_id,omitempty"`
	Model                    string `json:"model,omitempty"`
	MonitorID                int64  `json:"monitor_id,omitempty"`
	MonitorName              string `json:"monitor_name,omitempty"`
	FirmwareType             string `json:"firmware_type,omitempty"`
	FirmwareVersion          string `json:"firmware_version,omitempty"`
	MonitorCommVersion       string `json:"monitor_comm_version,omitempty"`
	RMSEngineFirmwareType    string `json:"rms_engine_firmware_type,omitempty"`
	RMSEngineFirmwareVersion string `json:"rms_engine_firmware_version,omitempty"`
	Logic24FirmwareType      string `json:"_24logic_firmware_type,omitempty"`
	Logic24FirmwareVersion   string `json:"_24logic_firmware_version,omitempty"`
	Manufacturer             string `json:"manufacturer,omitempty"`
}

// ─── LogMonitorReset ─────────────────────────────────────────────────────────

type Logs_16leip_LogMonitorReset struct {
	Record []Logs_16leip_LogMonitorResetRecord `json:"record"`
}

type Logs_16leip_LogMonitorResetRecord struct {
	DateTime  time.Time `json:"datetime"`
	ResetType string    `json:"reset_type"`
}

// ─── LogPreviousFail ──────────────────────────────────────────────────────────

type Logs_16leip_LogPreviousFail struct {
	Record []Logs_16leip_LogPreviousFailRecord `json:"record"`
}

type Logs_16leip_LogPreviousFailRecord struct {
	DateTime                          time.Time `json:"datetime"`
	Fault                             string    `json:"fault"`
	ACLine                            string    `json:"ac_line"`
	T48VDCSignalBus                   string    `json:"t48vdc_signal_bus"`
	RedEnable                         string    `json:"red_enable"`
	MCCoilEE                          string    `json:"mccoil_ee"`
	SpecialFunction1                  string    `json:"special_function1"`
	SpecialFunction2                  string    `json:"special_function2"`
	WDTMonitor                        string    `json:"wdt_monitor"`
	T24VDCInput                       string    `json:"t24vdc_input"`
	Temperature                       string    `json:"temperature"`
	LsFlashBit                        string    `json:"ls_flash_bit"`
	FaultStatus                       []string  `json:"channel_status_fault"`
	ChannelGreenStatus                []string  `json:"channel_status_green"`
	ChannelYellowStatus               []string  `json:"channel_status_yellow"`
	ChannelRedStatus                  []string  `json:"channel_status_red"`
	ChannelWalkStatus                 []string  `json:"channel_status_walk"`
	ChannelGreenFieldCheckStatus      []string  `json:"channel_field_check_status_green"`
	ChannelYellowFieldCheckStatus     []string  `json:"channel_field_check_status_yellow"`
	ChannelRedFieldCheckStatus        []string  `json:"channel_field_check_status_red"`
	ChannelWalkFieldCheckStatus       []string  `json:"channel_field_check_status_walk"`
	ChannelGreenRecurrentPulseStatus  []string  `json:"channel_recurrent_pulse_status_green"`
	ChannelYellowRecurrentPulseStatus []string  `json:"channel_recurrent_pulse_status_yellow"`
	ChannelRedRecurrentPulseStatus    []string  `json:"channel_recurrent_pulse_status_red"`
	ChannelWalkRecurrentPulseStatus   []string  `json:"channel_recurrent_pulse_status_walk"`
	ChannelGreenRMSVoltage            []int64   `json:"channel_rms_voltage_green"`
	ChannelYellowRMSVoltage           []int64   `json:"channel_rms_voltage_yellow"`
	ChannelRedRMSVoltage              []int64   `json:"channel_rms_voltage_red"`
	ChannelWalkRMSVoltage             []int64   `json:"channel_rms_voltage_walk"`
	NextConflictingChannels           []string  `json:"next_conflicting_channels"`
	ChannelRedCurrentStatus           []string  `json:"channel_current_status_red"`
	ChannelYellowCurrentStatus        []string  `json:"channel_current_status_yellow"`
	ChannelGreenCurrentStatus         []string  `json:"channel_current_status_green"`
	ChannelRedRMSCurrent              []int64   `json:"channel_rms_current_red"`
	ChannelYellowRMSCurrent           []int64   `json:"channel_rms_current_yellow"`
	ChannelGreenRMSCurrent            []int64   `json:"channel_rms_current_green"`
}

// ─── LogConfigurationChange ──────────────────────────────────────────────────

type Logs_16leip_LogConfigurationChange struct {
	Record []Logs_16leip_ConfigurationChangeLogRecord `json:"record"`
}

type Logs_16leip_ConfigurationChangeLogRecord struct {
	DateTime time.Time `json:"datetime"`

	// permissives per channel (1–16)
	Ch1Permissives  []string `json:"ch_1_permissives"`
	Ch2Permissives  []string `json:"ch_2_permissives"`
	Ch3Permissives  []string `json:"ch_3_permissives"`
	Ch4Permissives  []string `json:"ch_4_permissives"`
	Ch5Permissives  []string `json:"ch_5_permissives"`
	Ch6Permissives  []string `json:"ch_6_permissives"`
	Ch7Permissives  []string `json:"ch_7_permissives"`
	Ch8Permissives  []string `json:"ch_8_permissives"`
	Ch9Permissives  []string `json:"ch_9_permissives"`
	Ch10Permissives []string `json:"ch_10_permissives"`
	Ch11Permissives []string `json:"ch_11_permissives"`
	Ch12Permissives []string `json:"ch_12_permissives"`
	Ch13Permissives []string `json:"ch_13_permissives"`
	Ch14Permissives []string `json:"ch_14_permissives"`
	Ch15Permissives []string `json:"ch_15_permissives"`
	Ch16Permissives []string `json:"ch_16_permissives"`
	Ch17Permissives []string `json:"ch_17_permissives"`
	Ch18Permissives []string `json:"ch_18_permissives"`
	Ch19Permissives []string `json:"ch_19_permissives"`
	Ch20Permissives []string `json:"ch_20_permissives"`
	Ch21Permissives []string `json:"ch_21_permissives"`
	Ch22Permissives []string `json:"ch_22_permissives"`
	Ch23Permissives []string `json:"ch_23_permissives"`
	Ch24Permissives []string `json:"ch_24_permissives"`
	Ch25Permissives []string `json:"ch_25_permissives"`
	Ch26Permissives []string `json:"ch_26_permissives"`
	Ch27Permissives []string `json:"ch_27_permissives"`
	Ch28Permissives []string `json:"ch_28_permissives"`
	Ch29Permissives []string `json:"ch_29_permissives"`
	Ch30Permissives []string `json:"ch_30_permissives"`
	Ch31Permissives []string `json:"ch_31_permissives"`

	// configuration flags & settings (translated to strings)
	RedFailEnable                   []string `json:"red_fail_enable"`
	GreenYellowDualEnable           []string `json:"green_yellow_dual_indication_enable"`
	YellowRedDualEnable             []string `json:"red_yellow_dual_indication_enable"`
	GreenRedDualEnable              []string `json:"red_green_dual_indication_enable"`
	MinimumYellowClearanceEnable    []string `json:"minimum_yellow_clearance_disable_jumpers"`
	MinimumYellowRedClearanceEnable []string `json:"minimum_yellow_red_clearance_disable"`
	FieldCheckEnableGreen           []string `json:"field_check_enable_green"`
	FieldCheckEnableYellow          []string `json:"field_check_enable_yellow"`
	FieldCheckEnableRed             []string `json:"field_check_enable_red"`
	YellowEnable                    []string `json:"yellow_enable"`

	WalkEnableTs1        string `json:"walk_disable_ts1"`
	RedFaultTiming       string `json:"red_fault_timing"`
	RecurrentPulse       string `json:"recurrent_pulse_monitor"`
	WatchdogTiming       string `json:"watchdog_timing"`
	WatchdogEnableSwitch string `json:"external_watchdog_monitor"`
	ProgramCardMemory    string `json:"program_card_memory"`

	GYEnable         string `json:"gy_enable"`
	MinimumFlashTime string `json:"minimum_flash_time"`
	CvmLatchEnable   string `json:"cvm_latch_enable"`
	LogCvmFaults     string `json:"log_cvm_faults"`

	X24VIiInputThreshold string `json:"_24v_ii_input_threshold"`
	X24VLatchEnable      string `json:"_24v_latch_enable"`
	X24VoltInhibit       string `json:"_24volt_inhibit"`
	Port1Disable         string `json:"port_1_disable"`

	TypeMode           string `json:"type_mode"`
	LEDGuardThresholds string `json:"ledguard_thresholds"`
	ForceType16Mode    string `json:"force_type_16_mode"`
	Type12WithSdlcMode string `json:"type_12_with_sdlc_mode"`

	VmCvm24V3XDayLatch        string   `json:"vm_cvm_24v_3xday_latch"`
	RedFailEnabledBySSM       string   `json:"red_fail_enabled_by_ssm"`
	DualIndicationFaultTiming string   `json:"dual_indication_fault_timing"`
	WDTErrorClearOnPU         string   `json:"wdt_error_clear_on_pu"`
	MinimumFlash              string   `json:"minimum_flash"`
	ConfigChangeFault         string   `json:"config_change_fault"`
	RedCableFault             string   `json:"red_cable_fault"`
	AcLineBrownout            string   `json:"ac_line_brownout"`
	PinEEPolarity             string   `json:"pin_ee_polarity"`
	FlashingYellowArrows      []string `json:"flashing_yellow_arrow"`
	FyaRedAndYellowEnable     string   `json:"fya_red_and_yellow_enable"`
	FyaRedAndGreenDisable     string   `json:"fya_red_and_green_disable"`
	FyaYellowTrapDetection    string   `json:"fya_yellow_trap_detection"`
	FYAFlashRateFault         string   `json:"fya_flash_rate_fault"`
	FyaFlashRateDetection     string   `json:"fya_flash_rate_detection"`
	Pplt5Suppression          string   `json:"pplt5_suppression"`
	CheckValue                string   `json:"configuration_check_value"`
	ChangeSource              string   `json:"configuration_change_source"`
	// Virtual channel settings
	RedVirtualChannel    []Logs_16leip_VirtualSetting `json:"red_virtual_channel"`
	YellowVirtualChannel []Logs_16leip_VirtualSetting `json:"yellow_virtual_channel"`
	GreenVirtualChannel  []Logs_16leip_VirtualSetting `json:"green_virtual_channel"`
	// Current sense data
	CurrentSenseRedEnabled      []string `json:"current_sense_red_enabled"`
	CurrentSenseYellowEnabled   []string `json:"current_sense_yellow_enabled"`
	CurrentSenseGreenEnabled    []string `json:"current_sense_green_enabled"`
	CurrentSenseRedThreshold    []int    `json:"current_sense_red_threshold"`
	CurrentSenseYellowThreshold []int    `json:"current_sense_yellow_threshold"`
	CurrentSenseGreenThreshold  []int    `json:"current_sense_green_threshold"`
	// Dark Channel Maps data
	DarkChannelX01 []bool `json:"dark_channel_x01"`
	DarkChannelX02 []bool `json:"dark_channel_x02"`
	DarkChannelX03 []bool `json:"dark_channel_x03"`
	DarkChannelX04 []bool `json:"dark_channel_x04"`
}

// VirtualSetting represents a virtual channel setting for a specific color.
type Logs_16leip_VirtualSetting struct {
	Color         string `json:"color"`
	Enabled       bool   `json:"enabled"`
	SourceChannel int    `json:"source_channel"`
	SourceColor   string `json:"source_color"`
}

// ─── LogAcLineEvent ──────────────────────────────────────────────────────────

type Logs_16leip_LogAcLineEvent struct {
	Record      []Logs_16leip_AcLineEventRecord `json:"events"`
	VoltageType string                          `json:"voltage_type"`
}

type Logs_16leip_AcLineEventRecord struct {
	EventType       string    `json:"event_type"`
	DateTime        time.Time `json:"datetime"`
	LineVoltageRms  int64     `json:"line_voltage_vrms"`
	LineFrequencyHz int64     `json:"line_frequency_hz"`
}

// ─── LogFaultSignalSequence ──────────────────────────────────────────────────

type Logs_16leip_LogFaultSignalSequence struct {
	Record []Logs_16leip_FaultSignalSequenceRecord `json:"record"`
}

type Logs_16leip_FaultSignalSequenceRecord struct {
	FaultType string                          `json:"fault_type"`
	Buffers   []Logs_16leip_FaultSignalBuffer `json:"buffers"`
}

type Logs_16leip_FaultSignalBuffer struct {
	BufferRawBytes []byte `json:"buffer_raw_bytes"`
	Timestamp      int64  `json:"timestamp"`
	Red            []bool `json:"red"`
	Yellow         []bool `json:"yellow"`
	Green          []bool `json:"green"`
	Walk           []bool `json:"walk"`
	EeSfRe         bool   `json:"ee_sf_re"`
	AcVoltage      int64  `json:"ac_voltage"`
}

// ─── DeviceDetail ────────────────────────────────────────────────────────────

type PGDeviceDetail struct {
	DeviceID                 string `json:"device_id"`
	SoftWareGateWayID        string `json:"software_gateway_id"`
	MonitorID                int64  `json:"monitor_id"`
	MonitorName              string `json:"monitor_name"`
	RMSEngineFirmwareType    string `json:"rms_engine_firmware_type"`
	RMSEngineFirmwareVersion string `json:"rms_engine_firmware_version"`
	OrganizationID           string `json:"organization_id"`
}

type ProtobufSchema string

const (
	Mmu16le       ProtobufSchema = "fault_mmu2_16leip.proto"
	Ecl2010       ProtobufSchema = "fault_ecl2010_fplus.proto "
	CMU2212_hv    ProtobufSchema = "fault_cmu2212_base.proto"
	CMUip2212_hv  ProtobufSchema = "fault_cmu2212_base.proto"
	CMU2212_lv    ProtobufSchema = "fault_cmu2212_base.proto"
	CMUip2212_lv  ProtobufSchema = "fault_cmu2212_base.proto"
	CMU2212_vhv   ProtobufSchema = "fault_cmu2212_base.proto"
	CMUip2212_vhv ProtobufSchema = "fault_cmu2212_base.proto"
)

// logs_edi_cmu2212_base.go

type Logs_CMU2212_Base struct {
	LogData *Logs_CMU2212_Base_LogData `json:"log_data,omitempty"`
}

type Logs_CMU2212_Base_LogData struct {
	MessageTime            time.Time                                         `json:"message_time,omitempty"`
	DeviceId               string                                            `json:"device_id,omitempty"`
	Schema                 string                                            `json:"schema,omitempty"`
	DeviceDetail           *Logs_CMU2212_Base_LogData_DeviceDetail           `json:"device_detail,omitempty"`
	LogMonitorReset        *Logs_CMU2212_Base_LogData_LogMonitorReset        `json:"log_monitor_reset,omitempty"`
	LogPreviousFail        *Logs_CMU2212_Base_LogData_LogPreviousFail        `json:"log_previous_fail,omitempty"`
	LogConfigurationChange *Logs_CMU2212_Base_LogData_LogConfigurationChange `json:"log_configuration,omitempty"`
	LogAcLineEvent         *Logs_CMU2212_Base_LogData_LogACLineEvent         `json:"log_ac_line_event,omitempty"`
	LogFaultSignalSequence *Logs_CMU2212_Base_LogData_LogFaultSignalSequence `json:"log_fault_signal_sequence,omitempty"`
}

type Logs_CMU2212_Base_LogData_DeviceDetail struct {
	DeviceId                 string `json:"device_id,omitempty"`
	Model                    string `json:"model,omitempty"`
	MonitorId                int64  `json:"monitor_id,omitempty"`
	MonitorName              string `json:"monitor_name,omitempty"`
	FirmwareType             string `json:"firmware_type,omitempty"`
	FirmwareVersion          string `json:"firmware_version,omitempty"`
	MonitorCommVersion       string `json:"monitor_comm_version,omitempty"`
	RmsEngineFirmwareType    string `json:"rms_engine_firmware_type,omitempty"`
	RmsEngineFirmwareVersion string `json:"rms_engine_firmware_version,omitempty"`
	Logic24FirmwareType      string `json:"logic24_firmware_type,omitempty"`
	Logic24FirmwareVersion   string `json:"logic24_firmware_version,omitempty"`
	Manufacturer             string `json:"manufacturer,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogMonitorReset struct {
	Record []*Logs_CMU2212_Base_LogData_LogMonitorReset_LogMonitorResetRecord `json:"record,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogMonitorReset_LogMonitorResetRecord struct {
	Datetime time.Time `json:"datetime,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogPreviousFail struct {
	Record []*Logs_CMU2212_Base_LogData_LogPreviousFail_LogPreviousFailRecord `json:"record,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogPreviousFail_LogPreviousFailRecord struct {
	Datetime time.Time `json:"datetime,omitempty"`
	Fault    string    `json:"fault,omitempty"`
	AcLine   string    `json:"ac_line,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogConfigurationChange struct {
	Record []*Logs_CMU2212_Base_LogData_LogConfigurationChange_ConfigurationChangeLogRecord `json:"record,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogConfigurationChange_ConfigurationChangeLogRecord struct {
	Datetime time.Time `json:"datetime,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogACLineEvent struct {
	VoltageType int64                                                            `json:"voltage_type,omitempty"`
	Events      []*Logs_CMU2212_Base_LogData_LogACLineEvent_LogACLineEventRecord `json:"events,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogACLineEvent_LogACLineEventRecord struct {
	Datetime        time.Time `json:"datetime,omitempty"`
	EventType       string    `json:"event_type,omitempty"`
	LineVoltageVrms int64     `json:"line_voltage_vrms"`
}

type Logs_CMU2212_Base_LogData_LogFaultSignalSequence struct {
	Record []*Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord `json:"record,omitempty"`
}

type Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord struct {
	// empty record for CMU2212_Base
}

type Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer struct {
	Timestamp int64  `json:"timestamp"`
	Red       []bool `json:"red,omitempty"`
	Yellow    []bool `json:"yellow,omitempty"`
	Green     []bool `json:"green,omitempty"`
	EeSfRe    bool   `json:"ee_sf_re,omitempty"`
	AcVoltage int64  `json:"ac_voltage,omitempty"`
}

// logs_edi_ecl2010_fplus.go

type Logs_ECL2010_FPlus struct {
	LogData *Logs_ECL2010_FPlus_LogData `json:"log_data,omitempty"`
}

type Logs_ECL2010_FPlus_LogData struct {
	MessageTime            time.Time                                          `json:"message_time,omitempty"`
	DeviceId               string                                             `json:"device_id,omitempty"`
	Schema                 string                                             `json:"schema,omitempty"`
	DeviceDetail           *Logs_ECL2010_FPlus_LogData_DeviceDetail           `json:"device_detail,omitempty"`
	LogMonitorReset        *Logs_ECL2010_FPlus_LogData_LogMonitorReset        `json:"log_monitor_reset,omitempty"`
	LogPreviousFail        *Logs_ECL2010_FPlus_LogData_LogPreviousFail        `json:"log_previous_fail,omitempty"`
	LogConfigurationChange *Logs_ECL2010_FPlus_LogData_LogConfigurationChange `json:"log_configuration,omitempty"`
	LogAcLineEvent         *Logs_ECL2010_FPlus_LogData_LogACLineEvent         `json:"log_ac_line_event,omitempty"`
	LogFaultSignalSequence *Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence `json:"log_fault_signal_sequence,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_DeviceDetail struct {
	DeviceId                 string `json:"device_id,omitempty"`
	Model                    string `json:"model,omitempty"`
	MonitorId                int64  `json:"monitor_id,omitempty"`
	MonitorName              string `json:"monitor_name,omitempty"`
	FirmwareType             string `json:"firmware_type,omitempty"`
	FirmwareVersion          string `json:"firmware_version,omitempty"`
	MonitorCommVersion       string `json:"monitor_comm_version,omitempty"`
	RmsEngineFirmwareType    string `json:"rms_engine_firmware_type,omitempty"`
	RmsEngineFirmwareVersion string `json:"rms_engine_firmware_version,omitempty"`
	Logic24FirmwareType      string `json:"logic24_firmware_type,omitempty"`
	Logic24FirmwareVersion   string `json:"logic24_firmware_version,omitempty"`
	Manufacturer             string `json:"manufacturer,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogMonitorReset struct {
	Record []*Logs_ECL2010_FPlus_LogData_LogMonitorReset_LogMonitorResetRecord `json:"record,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogMonitorReset_LogMonitorResetRecord struct {
	Datetime time.Time `json:"datetime,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogPreviousFail struct {
	Record []*Logs_ECL2010_FPlus_LogData_LogPreviousFail_LogPreviousFailRecord `json:"record,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogPreviousFail_LogPreviousFailRecord struct {
	Datetime                          time.Time `json:"datetime"`
	Fault                             string    `json:"fault"`
	AcLine                            string    `json:"ac_line"`
	X48VdcSignalBus                   string    `json:"_48_vdc_signal_bus"`
	RedEnable                         string    `json:"red_enable"`
	McCoilEe                          string    `json:"mc_coil_ee"`
	SpecialFunction1                  string    `json:"special_function_1"`
	SpecialFunction2                  string    `json:"special_function_2"`
	WdtMonitor                        string    `json:"wdt_monitor"`
	X24VdcInput                       string    `json:"_24_vdc_input"`
	Temperature                       string    `json:"temperature"`
	FaultStatus                       []string  `json:"fault_status"`
	ChannelGreenStatus                []string  `json:"channel_green_status"`
	ChannelYellowStatus               []string  `json:"channel_yellow_status"`
	ChannelRedStatus                  []string  `json:"channel_red_status"`
	ChannelWalkStatus                 []string  `json:"channel_walk_status"`
	ChannelGreenFieldCheckStatus      []string  `json:"channel_green_field_check_status"`
	ChannelYellowFieldCheckStatus     []string  `json:"channel_yellow_field_check_status"`
	ChannelRedFieldCheckStatus        []string  `json:"channel_red_field_check_status"`
	ChannelWalkFieldCheckStatus       []string  `json:"channel_walk_field_check_status"`
	ChannelGreenRecurrentPulseStatus  []string  `json:"recurrent_pulse_status_green"`
	ChannelYellowRecurrentPulseStatus []string  `json:"recurrent_pulse_status_yellow"`
	ChannelRedRecurrentPulseStatus    []string  `json:"recurrent_pulse_status_red"`
	ChannelWalkRecurrentPulseStatus   []string  `json:"channel_walk_recurrent_pulse_status"`
	ChannelGreenRmsVoltage            []int64   `json:"channel_green_rms_voltage"`
	ChannelYellowRmsVoltage           []int64   `json:"channel_yellow_rms_voltage"`
	ChannelRedRmsVoltage              []int64   `json:"channel_red_rms_voltage"`
	ChannelWalkRmsVoltage             []int64   `json:"channel_walk_rms_voltage"`
	NextConflictingChannels           []int64   `json:"next_conflicting_channels"`
}

type Logs_ECL2010_FPlus_LogData_LogConfigurationChange struct {
	Record []*Logs_ECL2010_FPlus_LogData_LogConfigurationChange_ConfigurationChangeLogRecord `json:"record,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogConfigurationChange_ConfigurationChangeLogRecord struct {
	Datetime                     time.Time `json:"datetime,omitempty"`
	Ch_1Permissives              []string  `json:"ch_1_permissives,omitempty"`
	Ch_2Permissives              []string  `json:"ch_2_permissives,omitempty"`
	Ch_3Permissives              []string  `json:"ch_3_permissives,omitempty"`
	Ch_4Permissives              []string  `json:"ch_4_permissives,omitempty"`
	Ch_5Permissives              []string  `json:"ch_5_permissives,omitempty"`
	Ch_6Permissives              []string  `json:"ch_6_permissives,omitempty"`
	Ch_7Permissives              []string  `json:"ch_7_permissives,omitempty"`
	Ch_8Permissives              []string  `json:"ch_8_permissives,omitempty"`
	Ch_9Permissives              []string  `json:"ch_9_permissives,omitempty"`
	Ch_10Permissives             []string  `json:"ch_10_permissives,omitempty"`
	Ch_11Permissives             []string  `json:"ch_11_permissives,omitempty"`
	Ch_12Permissives             []string  `json:"ch_12_permissives,omitempty"`
	Ch_13Permissives             []string  `json:"ch_13_permissives,omitempty"`
	Ch_14Permissives             []string  `json:"ch_14_permissives,omitempty"`
	Ch_15Permissives             []string  `json:"ch_15_permissives,omitempty"`
	RedFailEnable                []string  `json:"red_fail_enable,omitempty"`
	GreenYellowDualEnable        []string  `json:"green_yellow_dual_enable,omitempty"`
	YellowRedDualEnable          []string  `json:"yellow_red_dual_enable,omitempty"`
	GreenRedDualEnable           []string  `json:"green_red_dual_enable,omitempty"`
	MinimumYellowClearanceEnable []string  `json:"minimum_yellow_clearance_enable,omitempty"`
	YellowDisable                []string  `json:"yellow_disable,omitempty"`
	RedFaultTiming               string    `json:"red_fault_timing,omitempty"`
	RecurrentPulse               string    `json:"recurrent_pulse,omitempty"`
	WatchdogTiming               string    `json:"watchdog_timing,omitempty"`
	WatchdogEnable               string    `json:"watchdog_enable_switch,omitempty"`
	GYEnable                     string    `json:"gy_enable,omitempty"`
	LedGuardThresholds           string    `json:"ledguard_thresholds,omitempty"`
	RedFailEnableBySSM           string    `json:"red_fail_enable_by_ssm,omitempty"`
	DualIndicationFaultTiming    string    `json:"dual_indication_fault_timing,omitempty"`
	WDTErrorClearOnPU            string    `json:"wdt_error_clear_on_pu,omitempty"`
	MinimumFlash                 string    `json:"minimum_flash,omitempty"`
	ConfigChangeFault            string    `json:"config_change_fault,omitempty"`
	RedCableFault                string    `json:"red_cable_fault,omitempty"`
	AcLineBrownout               string    `json:"ac_line_brownout,omitempty"`
	PinEEPolarity                string    `json:"pin_ee_polarity,omitempty"`
	FlashingYellowArrows         []string  `json:"flashing_yellow_arrow,omitempty"`
	FyaFlashRateFault            string    `json:"fya_flash_rate_fault,omitempty"`
	CheckValue                   string    `json:"configuration_check_value,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogACLineEvent struct {
	VoltageType int64                                                             `json:"voltage_type,omitempty"`
	Events      []*Logs_ECL2010_FPlus_LogData_LogACLineEvent_LogACLineEventRecord `json:"events,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogACLineEvent_LogACLineEventRecord struct {
	EventType       string    `json:"event_type,omitempty"`
	Datetime        time.Time `json:"datetime,omitempty"`
	LineVoltageVrms int64     `json:"line_voltage_vrms"`
	LineFrequencyHz int64     `json:"line_frequency_hz"`
}

type Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence struct {
	Record []*Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord `json:"record,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord struct {
	FaultType string                                                                                           `json:"fault_type,omitempty"`
	Buffers   []*Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer `json:"buffers,omitempty"`
}

type Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer struct {
	BufferRawBytes []byte `json:"buffer_raw_bytes,omitempty"`
	Timestamp      int64  `json:"timestamp"`
	Red            []bool `json:"red,omitempty"`
	Yellow         []bool `json:"yellow,omitempty"`
	Green          []bool `json:"green,omitempty"`
	Walk           []bool `json:"walk,omitempty"`
	EeSfRe         bool   `json:"ee_sf_re,omitempty"`
	AcVoltage      int64  `json:"ac_voltage,omitempty"`
}
