package fault

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	authorizer "synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/schemas"

	"github.com/stretchr/testify/assert"
)

// backup & restore our package-level overrides
var (
	origUserPermissionsFromContext = userPermissionsFromContext
	origParseRequest               = parseRequest
	origGetPGDeviceDetail          = getPGDeviceDetail
	origGetBQLogs                  = getBQLogs
	origJSONMarshalIndent          = jsonMarshalIndentFn
	origJSONClean                  = jsonCleanFn
)

func teardown() {
	userPermissionsFromContext = origUserPermissionsFromContext
	parseRequest = origParseRequest
	getPGDeviceDetail = origGetPGDeviceDetail
	getBQLogs = origGetBQLogs
	jsonMarshalIndentFn = origJSONMarshalIndent
	jsonCleanFn = origJ<PERSON><PERSON>lean
}

// a tiny helper to grab HTTP status codes
func doRequest(t *testing.T, req *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()
	Handler(w, req)
	return w
}

func TestHandler_UserInfoMissing(t *testing.T) {
	defer teardown()
	// simulate no JWT info
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return nil, false
	}

	req := httptest.NewRequest("GET", "/?deviceid=42", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_ParseRequestError(t *testing.T) {
	defer teardown()
	// stub valid JWT
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "1"}, true
	}
	// stub parseRequest to fail
	parseRequest = func(r *http.Request) (int64, error) {
		return 0, errors.New("bad query")
	}

	req := httptest.NewRequest("GET", "/?deviceid=notanint", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestHandler_GetConnectionsError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "2"}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 123, nil
	}

	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return nil, errors.New("fail connect")
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := httptest.NewRequest("GET", "/?deviceid=123", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_CanAccessDeviceError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "3"}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 777, nil
	}

	// Create a fake database executor that will fail for CanAccessDeviceByOrigID
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Check if this is the CanAccessDeviceByOrigID query
			if strings.Contains(query, "d.OrigID = $1") {
				return errors.New("access error")
			}
			return nil
		},
	}

	// make GetConnections succeed with our fake DB
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := httptest.NewRequest("GET", "/?deviceid=777", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_CanAccessDeviceUnauthorized(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "4"}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 888, nil
	}

	// Create a fake database executor that returns no device (empty string UUID)
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// For CanAccessDeviceByOrigID query, return no error but don't populate the struct
			// This will result in empty DeviceID which means no access
			return nil
		},
	}

	// make GetConnections succeed with our fake DB
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := httptest.NewRequest("GET", "/?deviceid=888", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestHandler_PGDeviceDetailError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "5",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 999, nil
	}

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID but fails for getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-123"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, return an error
			return errors.New("db error")
		},
	}

	// make GetConnections succeed with our fake DB
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := httptest.NewRequest("GET", "/?deviceid=999", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_BQLogsError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "4",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 888, nil
	}

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-888"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev"
				pgDetail.SoftWareGateWayID = "gw"
				pgDetail.OrganizationID = "org"
			}
			return nil
		},
	}

	// make GetConnections & PG succeed
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// stub BQ to error
	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, softwaregatewayID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{}, errors.New("bq error")
	}

	req := httptest.NewRequest("GET", "/?deviceid=888", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_Success_DefaultSchema(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "5",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 999, nil
	}

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-999"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev"
				pgDetail.SoftWareGateWayID = "gw"
				pgDetail.OrganizationID = "org"
				pgDetail.MonitorID = 11
				pgDetail.MonitorName = "X"
				pgDetail.RMSEngineFirmwareType = "1"
				pgDetail.RMSEngineFirmwareVersion = "2"
			}
			return nil
		},
	}

	// stub connect & PG
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// stub BQ to return zero‐valued AllLogs with a Model that maps via default case
	getBQLogs = func(
		bq connect.BigQueryExecutorInterface,
		deviceID, softwaregatewayID, orgID string,
		filterDays ...int,
	) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(99999), // hits default → 16leip
				DeviceModel:        "",
				FirmwareReVision:   "0",
				FirmwareVersion:    "0",
				MonitorCommVersion: "0",
				PubsubTimestamp:    time.Time{}, // zero‐value
				RawLogMessages:     schemas.RawLogMessages{},
				// all other fields on FaultLogsWithModel default to zero
			},
			// LogMonitorReset, LogPreviousFail, LogConfiguration,
			// LogACLineEvent and LogFaultSignalSequence are omitted
			// and will default to their zero‐values—satisfying the converter.
		}, nil
	}

	req := httptest.NewRequest("GET", "/?deviceid=999", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

// Test the ECL2010_FPlus path
func TestHandler_Success_ECL2010FPlusSchema(t *testing.T) {
	defer teardown()

	// 1) Stub JWT and request parsing
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "100",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "orgID",
					OrganizationID: "orgID",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 555, nil
	}

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-555"
					deviceInfo.OrganizationID = "orgID"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "devID"
				pgDetail.SoftWareGateWayID = "gwID"
				pgDetail.OrganizationID = "orgID"
				pgDetail.MonitorID = 7
				pgDetail.MonitorName = "MyMon"
				pgDetail.RMSEngineFirmwareType = "9"
				pgDetail.RMSEngineFirmwareVersion = "10"
			}
			return nil
		},
	}

	// 2) Stub connections + Postgres lookup
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// 3) Stub BQ to return ECL2010 model
	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, gwID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(3), // ECL2010 model (edihelper.Ecl2010 = 3)
				DeviceModel:        "MyModel",
				FirmwareReVision:   "2",
				FirmwareVersion:    "3",
				MonitorCommVersion: "4",
				PubsubTimestamp:    time.Now(),
				RawLogMessages:     schemas.RawLogMessages{},
			},
		}, nil
	}

	// 4) Exercise handler
	req := httptest.NewRequest("GET", "/?deviceid=555", nil)
	w := doRequest(t, req)

	// 5) Verify 200 OK and that the response is valid JSON containing our device model
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), `"model":"MyModel"`)
	assert.Contains(t, w.Body.String(), `"schema":"fault_ecl2010_fplus.proto "`)
}

// Test the CMU2212_Base path
func TestHandler_Success_CMU2212BaseSchema(t *testing.T) {
	defer teardown()

	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "101",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "orgID2",
					OrganizationID: "orgID2",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 777, nil
	}

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-777"
					deviceInfo.OrganizationID = "orgID2"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "devID2"
				pgDetail.SoftWareGateWayID = "gwID2"
				pgDetail.OrganizationID = "orgID2"
				pgDetail.MonitorID = 8
				pgDetail.MonitorName = "YourMon"
				pgDetail.RMSEngineFirmwareType = "11"
				pgDetail.RMSEngineFirmwareVersion = "12"
			}
			return nil
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, gwID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(51), // CMUip2212_hv model (edihelper.CMUip2212_hv = 51)
				DeviceModel:        "YourModel",
				FirmwareReVision:   "5",
				FirmwareVersion:    "6",
				MonitorCommVersion: "7",
				PubsubTimestamp:    time.Now(),
				RawLogMessages:     schemas.RawLogMessages{},
			},
		}, nil
	}

	req := httptest.NewRequest("GET", "/?deviceid=777", nil)
	w := doRequest(t, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), `"model":"YourModel"`)
	assert.Contains(t, w.Body.String(), `"schema":"fault_cmu2212_base.proto"`)
}

func TestHandler_JSONMarshalError(t *testing.T) {
	defer teardown()
	// normal user + parse + connect + PG + BQ success stubs...
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "200",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "o",
					OrganizationID: "o",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) { return 1, nil }

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-1"
					deviceInfo.OrganizationID = "o"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "d"
				pgDetail.SoftWareGateWayID = "g"
				pgDetail.MonitorID = 0
				pgDetail.MonitorName = ""
				pgDetail.RMSEngineFirmwareType = "0"
				pgDetail.RMSEngineFirmwareVersion = "0"
				pgDetail.OrganizationID = "o"
			}
			return nil
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getBQLogs = func(bq connect.BigQueryExecutorInterface, did, gid, oid string, f ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{FaultLogs: schemas.FaultLogsWithModel{Model: int64(11)}}, nil // Mmu16le = 11
	}

	// force JSON‐marshal error
	jsonMarshalIndentFn = func(v interface{}, prefix, indent string) ([]byte, error) {
		return nil, errors.New("marshal fail")
	}

	w := doRequest(t, httptest.NewRequest("GET", "/?deviceid=1", nil))
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_JSONCleanError(t *testing.T) {
	defer teardown()
	// same stubs for everything
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "201",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "o",
					OrganizationID: "o",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) { return 1, nil }

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-1"
					deviceInfo.OrganizationID = "o"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "d"
				pgDetail.SoftWareGateWayID = "g"
				pgDetail.MonitorID = 0
				pgDetail.MonitorName = ""
				pgDetail.RMSEngineFirmwareType = "0"
				pgDetail.RMSEngineFirmwareVersion = "0"
				pgDetail.OrganizationID = "o"
			}
			return nil
		},
	}

	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getBQLogs = func(bq connect.BigQueryExecutorInterface, did, gid, oid string, f ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{FaultLogs: schemas.FaultLogsWithModel{Model: int64(11)}}, nil // Mmu16le = 11
	}

	// JSON‐marshal works normally
	jsonMarshalIndentFn = origJSONMarshalIndent
	// force JSON‐clean error
	jsonCleanFn = func(raw []byte) ([]byte, error) {
		return nil, errors.New("clean fail")
	}

	w := doRequest(t, httptest.NewRequest("GET", "/?deviceid=1", nil))
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_BQLogsInternalError(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "6",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 777, nil
	}

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-777"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev-777"
				pgDetail.SoftWareGateWayID = "gw-777"
				pgDetail.OrganizationID = "org-777"
			}
			return nil
		},
	}

	// Create a fake BigQuery executor that returns an error
	fakeBQ := &mocks.FakeBigQueryExecutor{}
	fakeBQ.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		return errors.New("bigquery internal error")
	}

	// make GetConnections succeed with our fake DB and BQ
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	mockConnections.Bigquery = fakeBQ
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// DON'T override getBQLogs - we want to test the real function's error handling

	req := httptest.NewRequest("GET", "/?deviceid=777", nil)
	w := doRequest(t, req)

	// The getBQLogs function returns the error, causing the handler to return 500
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_BQLogsWithCustomDays(t *testing.T) {
	defer teardown()
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{
			UserID: "6",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "org-123",
					OrganizationID: "org-123",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}
	parseRequest = func(r *http.Request) (int64, error) {
		return 777, nil
	}

	// Create a fake database executor that succeeds for CanAccessDeviceByOrigID and getPGDeviceDetail
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			if strings.Contains(query, "d.OrigID = $1") {
				// This is CanAccessDeviceByOrigID - populate with a device UUID
				if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
					deviceInfo.DeviceID = "device-uuid-777"
					deviceInfo.OrganizationID = "org-123"
					deviceInfo.DeviceGroupIDs = []string{}
					deviceInfo.LocationGroupIDs = []string{}
				}
				return nil
			}
			// For getPGDeviceDetail query, succeed
			if pgDetail, ok := dest.(*PGDeviceDetail); ok {
				pgDetail.DeviceID = "dev-777"
				pgDetail.SoftWareGateWayID = "gw-777"
				pgDetail.OrganizationID = "org-777"
			}
			return nil
		},
	}

	// Create a fake BigQuery executor that succeeds and verifies the custom days parameter
	fakeBQ := &mocks.FakeBigQueryExecutor{}
	fakeBQ.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		// Verify that the first argument (days) is our custom value
		if len(args) > 0 {
			if days, ok := args[0].(int); ok && days == 7 {
				// This confirms the filterDays parameter was used
				t.Logf("Custom days parameter %d was used correctly", days)
			}
		}
		// Populate with some dummy data
		if allLogs, ok := dest.(*schemas.AllLogs); ok {
			allLogs.FaultLogs.Model = 3 // ECL2010 model
		}
		return nil
	}

	// Override getBQLogs to use custom days parameter
	origGetBQLogs := getBQLogs
	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID string, softwaregatewayID string, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		// Call the original function with custom days parameter (7 days instead of default 90)
		return origGetBQLogs(bq, deviceID, softwaregatewayID, orgID, 7)
	}
	defer func() { getBQLogs = origGetBQLogs }()

	// make GetConnections succeed with our fake DB and BQ
	origGetConns := connect.GetConnections
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB
	mockConnections.Bigquery = fakeBQ
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return mockConnections, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := httptest.NewRequest("GET", "/?deviceid=777", nil)
	w := doRequest(t, req)

	// Should succeed with ECL2010 schema
	assert.Equal(t, http.StatusOK, w.Code)
}
