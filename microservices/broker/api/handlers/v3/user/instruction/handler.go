package instruction

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"synapse-its.com/broker/api/brokerShared"

	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserPermissionsFromContext func(ctx context.Context) (*authorizer.UserPermissions, bool)
	GetConnections             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseRequest               func(r *http.Request) (*userInstructionRequest, error)
	ValidateDeviceAccess       func(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, deviceOrigID int64, deviceUUID string) (string, error)
	InsertInstruction          func(db connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, deviceOrigID int64, deviceUUID string, instruction string) error
}

// The map of supported instructions
var supportedInstructions = map[string]bool{
	"get_device_logs": true,
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info from jwt authorizer using dependency
		userPermissions, ok := deps.UserPermissionsFromContext(ctx)
		if !ok {
			logger.Error(ErrUserInfoRetrieve.Error())
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse and validate userInstructionRequest
		requestBody, err := deps.ParseRequest(r)
		if err != nil {
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse the device_id field to determine if it's an int64 (OrigID) or UUID string
		deviceOrigID, deviceUUID, err := brokerShared.ParseInt64OrUUID(requestBody.DeviceID)
		if err != nil {
			logger.Infof("Error parsing device_id: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate device access
		deviceUUID, err = deps.ValidateDeviceAccess(pg, userPermissions, deviceOrigID, deviceUUID)
		if err != nil {
			logger.Errorf("Error validating device access: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// InsertInstruction stores a new device instruction in the database
		if err := deps.InsertInstruction(pg, userPermissions, deviceOrigID, deviceUUID, requestBody.Instruction); err != nil {
			logger.Errorf("Error inserting instruction: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse("", w)
	}
}

// Handler is the production-ready HTTP handler using default dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	UserPermissionsFromContext: authorizer.UserPermissionsFromContext,
	GetConnections:             connect.GetConnections,
	ParseRequest:               parseRequest,
	ValidateDeviceAccess:       validateDeviceAccess,
	InsertInstruction:          insertInstruction,
})

// Parse request body and validates the instruction
func parseRequest(r *http.Request) (*userInstructionRequest, error) {
	var body userInstructionRequest
	decorder := json.NewDecoder(r.Body)
	err := decorder.Decode(&body)
	if err != nil {
		logger.Error(ErrDecodeRequest.Error())
		return nil, ErrDecodeRequest
	}

	// Validate the instruction passed in is valid
	if !supportedInstructions[body.Instruction] {
		logger.Error(ErrInvalidInstructionRequest.Error())
		return nil, ErrInvalidInstructionRequest
	}

	return &body, nil
}

func validateDeviceAccess(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, deviceOrigID int64, deviceUUID string) (string, error) {
	// Check device access permissions based on which ID type was provided
	requiredPermissions := []string{"device_group_manage_devices", "location_group_manage_devices", "org_manage_devices"}
	var canAccess bool
	var err error

	if deviceUUID != "" {
		// Using UUID - preferred method
		canAccess, err = userPermissions.CanAccessDevice(pg, deviceUUID, requiredPermissions...)
		if err != nil {
			return "", fmt.Errorf("failed to check device access by UUID: %w", err)
		}
	} else {
		// Using OrigID - deprecated method
		deviceUUID, err = userPermissions.CanAccessDeviceByOrigID(pg, deviceOrigID, requiredPermissions...)
		if err != nil {
			return "", fmt.Errorf("failed to check device access by OrigID: %w", err)
		}
		canAccess = deviceUUID != ""
	}

	if !canAccess {
		if deviceUUID != "" {
			return "", fmt.Errorf("user does not have permission to manage device %s", deviceUUID)
		} else {
			return "", fmt.Errorf("user does not have permission to manage device %d", deviceOrigID)
		}
	}

	return deviceUUID, nil
}

// insertInstruction inserts into SoftwareGatewayInstruction table
func insertInstruction(pg connect.DatabaseExecutor, userPermissions *authorizer.UserPermissions, deviceOrigID int64, deviceUUID string, instruction string) error {
	// Insert the instruction
	query := `
		INSERT INTO {{SoftwareGatewayInstruction}} (
			UserId,
			DeviceId,
			Instruction,
			DateQueued,
			Status
		) VALUES ($1, COALESCE($2, (SELECT d.Id FROM {{Device}} d WHERE d.OrigID = $3)), $4, $5, 'queued')`

	result, err := pg.Exec(query, userPermissions.UserID, deviceUUID, deviceOrigID, instruction, time.Now().UTC())
	if err != nil {
		return fmt.Errorf("failed to insert instruction: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no instruction was inserted")
	}

	logger.Debugf("(%v) instruction delivered - userId: (%v), deviceId: (%v) instruction set", rowsAffected, userPermissions.UserID, deviceUUID)
	return nil
}
