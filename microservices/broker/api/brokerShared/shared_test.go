package brokerShared

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_ParseInt64OrUUID(t *testing.T) {
	t.<PERSON>llel()

	tests := []struct {
		name           string
		deviceIDRaw    json.RawMessage
		expectedOrigID int64
		expectedUUID   string
		expectedError  string
	}{
		{
			name:           "valid_orig_id_integer",
			deviceIDRaw:    json.RawMessage("123"),
			expectedOrigID: 123,
			expectedUUID:   "",
			expectedError:  "",
		},
		{
			name:           "valid_uuid_string",
			deviceIDRaw:    json.RawMessage(`"550e8400-e29b-41d4-a716-************"`),
			expectedOrigID: 0,
			expectedUUID:   "550e8400-e29b-41d4-a716-************",
			expectedError:  "",
		},
		{
			name:           "valid_uuid_uppercase",
			deviceIDRaw:    json.RawMessage(`"550E8400-E29B-41D4-A716-************"`),
			expectedOrigID: 0,
			expectedUUID:   "550e8400-e29b-41d4-a716-************", // Should be normalized to lowercase
			expectedError:  "",
		},
		{
			name:           "valid_orig_id_as_string",
			deviceIDRaw:    json.RawMessage(`"123456"`),
			expectedOrigID: 123456,
			expectedUUID:   "",
			expectedError:  "",
		},
		{
			name:           "empty_device_id",
			deviceIDRaw:    json.RawMessage(""),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID is required",
		},
		{
			name:           "nil_device_id",
			deviceIDRaw:    nil,
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID is required",
		},
		{
			name:           "invalid_uuid_format",
			deviceIDRaw:    json.RawMessage(`"not-a-valid-uuid"`),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID string is neither a valid UUID nor a valid integer",
		},
		{
			name:           "invalid_uuid_missing_hyphens",
			deviceIDRaw:    json.RawMessage(`"550e8400e29b41d4a716************"`),
			expectedOrigID: 0,
			expectedUUID:   "550e8400-e29b-41d4-a716-************",
			expectedError:  "",
		},
		{
			name:           "invalid_json_type",
			deviceIDRaw:    json.RawMessage(`{"invalid": "object"}`),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID must be either an integer (OrigID) or a UUID string",
		},
		{
			name:           "boolean_value",
			deviceIDRaw:    json.RawMessage("true"),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID must be either an integer (OrigID) or a UUID string",
		},
		{
			name:           "array_value",
			deviceIDRaw:    json.RawMessage("[1,2,3]"),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID must be either an integer (OrigID) or a UUID string",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			origID, uuid, err := ParseInt64OrUUID(tt.deviceIDRaw)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Equal(t, int64(0), origID)
				assert.Equal(t, "", uuid)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedOrigID, origID)
				assert.Equal(t, tt.expectedUUID, uuid)
			}
		})
	}
}
