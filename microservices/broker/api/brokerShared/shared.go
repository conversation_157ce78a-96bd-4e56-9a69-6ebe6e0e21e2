package brokerShared

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/google/uuid"
)

// ParseInt64OrUUID parses the json.RawMessage to determine if it's an int64 (OrigID) or UUID string
// Returns (origID, uuid, error) where exactly one of origID or uuid will be non-zero/non-empty
func ParseInt64OrUUID(IDRaw json.RawMessage) (int64, string, error) {
	// If the input is empty or nil, return an error
	if len(IDRaw) == 0 || IDRaw == nil {
		return 0, "", fmt.Errorf("ID is required")
	}

	// Try to parse as string first to check if it's a UUID
	var uuidStr string
	if err := json.Unmarshal(IDRaw, &uuidStr); err == nil {
		// Use the google/uuid library to validate UUID format
		if parsedUUID, err := uuid.Parse(uuidStr); err == nil {
			return 0, parsedUUID.String(), nil
		}
		// If it's a string but not a valid UUID, try parsing as int64
		if origID, err := strconv.ParseInt(uuidStr, 10, 64); err == nil {
			return origID, "", nil
		}
		return 0, "", fmt.Errorf("ID string is neither a valid UUID nor a valid integer")
	}

	// If it's not a string, try to parse as int64
	var origID int64
	if err := json.Unmarshal(IDRaw, &origID); err == nil {
		return origID, "", nil
	}

	return 0, "", fmt.Errorf("ID must be either an integer (OrigID) or a UUID string")
}
