package synapse

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	fakeps "synapse-its.com/shared/mocks/pubsub"
)

func TestNewPurgeExpiredHandler(t *testing.T) {
	tests := []struct {
		name           string
		mockSetup      func(*fakeps.FakePubsubClient)
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "successful publish",
			mockSetup:      func(fc *fakeps.FakePubsubClient) {},
			expectedStatus: http.StatusOK,
			expectedBody:   `{"code":200,"data":{"Attributes":{"topic":"broker-purge-expired-data"},"Data":null,"DeliveryAttempt":null,"ID":"","OrderingKey":"","PublishTime":"0001-01-01T00:00:00Z"},"message":"Request Succeeded","status":"success"}`,
		},
		{
			name:           "connection error",
			mockSetup:      func(fc *fakeps.FakePubsubClient) {},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   `{"code":500,"data":null,"message":"Internal Server Error","status":"error"}`,
		},
		{
			name: "publish error",
			mockSetup: func(fc *fakeps.FakePubsubClient) {
				fc.PublishError = errors.New("publish error")
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   `{"code":500,"data":null,"message":"Internal Server Error","status":"error"}`,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			fakeClient := fakeps.NewFakePubsubClient()
			if tt.mockSetup != nil {
				tt.mockSetup(fakeClient)
			}
			handler := newPurgeExpiredHandler(purgeExpiredDeps{
				getConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					if tt.name == "connection error" {
						return nil, assert.AnError
					}
					return &connect.Connections{Pubsub: fakeClient}, nil
				},
			})
			req := httptest.NewRequest(http.MethodPost, "/purge-expired", nil)
			w := httptest.NewRecorder()
			handler(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.JSONEq(t, tt.expectedBody, w.Body.String())
			if tt.expectedStatus == http.StatusOK {
				topic := fakeClient.Topic(topicName).(*fakeps.FakePubsubTopic)
				messages := topic.GetMessages()
				assert.Len(t, messages, 1)
				assert.Equal(t, topicName, messages[0].Attributes["topic"])
			}
		})
	}
}
