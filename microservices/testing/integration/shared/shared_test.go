package shared

import (
	"context"
	"testing"

	"cloud.google.com/go/bigquery"
	"github.com/google/go-cmp/cmp"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/schemas"
)

func cloneAndNormalize(in bigquery.Schema) bigquery.Schema {
	var out bigquery.Schema
	for _, f := range in {
		nf := *f            // shallow copy FieldSchema
		nf.Required = false // ignore nullability
		if len(f.Schema) > 0 {
			nf.Schema = cloneAndNormalize(f.Schema)
		}
		out = append(out, &nf)
	}
	return out
}

func TestSchemasMatchBQTables(t *testing.T) {
	ctx := context.Background()
	conn := connect.NewConnections(ctx)
	defer conn.Close()
	client := conn.Bigquery.GetClient()
	conf := conn.Bigquery.GetConfig()

	tests := []struct {
		name    string
		table   string
		example interface{}
	}{
		{"EtlRawMessages", schemas.T_EdiRawMessages, schemas.EdiRawMessages{}},
		{"RmsEngine", schemas.T_RmsEngine, schemas.RmsEngine{}},
		{"DlqMessages", schemas.T_DlqMessages, schemas.DlqMessages{}},
		{"MonitorName", schemas.T_MonitorName, schemas.MonitorName{}},
		{"MacAddress", schemas.T_MacAddress, schemas.MacAddress{}},
		{"RmsData", schemas.T_RmsData, schemas.RmsData{}},
		{"FaultNotification", schemas.T_FaultNotification, schemas.FaultNotification{}},
		{"GatewayPerformanceStatistics", schemas.T_GatewayPerformanceStatistics, schemas.GatewayPerformanceStatistics{}},
		{"FaultLogs", schemas.T_FaultLogs, schemas.FaultLogs{}},
		{"LogMonitorReset", schemas.T_LogMonitorReset, schemas.LogMonitorReset{}},
		{"LogPreviousFail", schemas.T_logPreviousFail, schemas.LogPreviousFail{}},
		{"LogACLineEvent", schemas.T_logACLineEvent, schemas.LogACLineEvent{}},
		{"LogFaultSignalSequence", schemas.T_logFaultSignalSequence, schemas.LogFaultSignalSequence{}},
		{"LogConfiguration", schemas.T_logConfiguration, schemas.LogConfiguration{}},
		{"NotificationMessages", schemas.T_NotificationMessages, schemas.NotificationMessages{}},
		{"BatchPerformanceStats", schemas.T_BatchPerformanceStats, schemas.BatchPerformanceStats{}},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			tbl := client.Dataset(conf.DBName).Table(connect.CombineTableNamespace(conf.Namespace, tc.table))
			meta, err := tbl.Metadata(ctx)
			if err != nil {
				t.Fatalf("Table.Metadata(%s): %v", connect.CombineTableNamespace(conf.Namespace, tc.table), err)
			}

			inferred, err := bigquery.InferSchema(tc.example)
			if err != nil {
				t.Fatalf("InferSchema(%T): %v", tc.example, err)
			}

			normInf := cloneAndNormalize(inferred)
			normMeta := cloneAndNormalize(meta.Schema)

			if diff := cmp.Diff(normInf, normMeta); diff != "" {
				t.Errorf("schema mismatch for %s (table %s):\n%s", tc.name, tc.table, diff)
			}
		})
	}
}
